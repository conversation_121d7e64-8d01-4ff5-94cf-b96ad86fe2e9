# Development dependencies
-r requirements.txt

# Testing
pytest>=7.0.0
pytest-asyncio>=0.21.0
pytest-cov>=4.0.0
pytest-mock>=3.10.0
pytest-html>=4.0.0

# Code quality
black>=23.0.0
flake8>=6.0.0
isort>=5.12.0
mypy>=1.0.0
types-PyYAML>=6.0.0
typing-extensions>=4.5.0

# Documentation
sphinx>=7.0.0
sphinx-rtd-theme>=1.2.0

# Development tools
twine>=4.0.0
build>=0.10.0
pre-commit>=3.0.0

# Debugging
ipython>=8.0.0
pdbpp>=0.10.0
