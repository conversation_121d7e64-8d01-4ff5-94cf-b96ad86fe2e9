#!/usr/bin/env python3
"""
Test script for MCP tools integration.
"""
import sys
import asyncio
import logging
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).parent))

from src.mcp import MCPServerManager
from src.utils.logger import setup_logging

async def test_local_tools():
    """Test the local MCP tools."""
    print("🛠️ Testing Cherry Studio Clone MCP Local Tools...")
    
    # Set up logging
    setup_logging(log_level='INFO', log_file='logs/test_mcp.log')
    logger = logging.getLogger(__name__)
    
    try:
        # Initialize MCP manager
        logger.info("Initializing MCP manager...")
        mcp_manager = MCPServerManager()
        
        # Get available tools
        tools = mcp_manager.get_all_tools()
        print(f"\n✅ Found {len(tools)} available tools:")
        
        for tool in tools:
            params = [f"{p.name}({p.type.value})" for p in tool.parameters[:3]]
            params_str = ", ".join(params) if params else "none"
            print(f"   🔧 {tool.name}: {tool.description}")
            print(f"      Parameters: {params_str}")
        
        print("\n" + "="*60)
        print("🧪 Testing Local Tools:")
        print("="*60)
        
        # Test 1: Get current time
        print("\n1. Testing get_current_time...")
        result = await mcp_manager.call_tool("get_current_time", {})
        if not result.is_error:
            print(f"   ✅ {result.content[0]['text']}")
        else:
            print(f"   ❌ {result.content[0]['text']}")
        
        # Test 2: Get system info
        print("\n2. Testing get_system_info...")
        result = await mcp_manager.call_tool("get_system_info", {})
        if not result.is_error:
            info_text = result.content[0]['text']
            print(f"   ✅ System info retrieved:")
            for line in info_text.split('\n')[:5]:  # Show first 5 lines
                print(f"      {line}")
            print("      ...")
        else:
            print(f"   ❌ {result.content[0]['text']}")
        
        # Test 3: Calculate
        print("\n3. Testing calculate...")
        result = await mcp_manager.call_tool("calculate", {"expression": "2 + 2 * 3"})
        if not result.is_error:
            print(f"   ✅ {result.content[0]['text']}")
        else:
            print(f"   ❌ {result.content[0]['text']}")
        
        # Test 4: List directory
        print("\n4. Testing list_directory...")
        result = await mcp_manager.call_tool("list_directory", {"path": "."})
        if not result.is_error:
            dir_text = result.content[0]['text']
            lines = dir_text.split('\n')
            print(f"   ✅ Directory listing (first 10 items):")
            for line in lines[1:11]:  # Skip header, show first 10 items
                if line.strip():
                    print(f"      {line}")
        else:
            print(f"   ❌ {result.content[0]['text']}")
        
        # Test 5: Create and write file
        print("\n5. Testing write_file...")
        test_content = "🍒 This is a test file created by Cherry Studio Clone MCP tools!"
        result = await mcp_manager.call_tool("write_file", {
            "path": "test_mcp_output.txt",
            "content": test_content
        })
        if not result.is_error:
            print(f"   ✅ {result.content[0]['text']}")
            
            # Test reading the file back
            print("\n6. Testing read_file...")
            result = await mcp_manager.call_tool("read_file", {"path": "test_mcp_output.txt"})
            if not result.is_error:
                read_content = result.content[0]['text']
                print(f"   ✅ Read back: {read_content}")
            else:
                print(f"   ❌ {result.content[0]['text']}")
        else:
            print(f"   ❌ {result.content[0]['text']}")
        
        # Test 6: Search text
        print("\n7. Testing search_text...")
        result = await mcp_manager.call_tool("search_text", {
            "text": "Hello world! This is a test. Hello again!",
            "pattern": "Hello"
        })
        if not result.is_error:
            print(f"   ✅ Search result:")
            search_text = result.content[0]['text']
            for line in search_text.split('\n')[:3]:  # Show first 3 lines
                if line.strip():
                    print(f"      {line}")
        else:
            print(f"   ❌ {result.content[0]['text']}")
        
        print("\n" + "="*60)
        print("🎉 MCP Local Tools Test Complete!")
        print("="*60)
        
        # Cleanup
        await mcp_manager.cleanup()
        
        # Try to clean up test file
        try:
            Path("test_mcp_output.txt").unlink(missing_ok=True)
        except:
            pass
        
        print("\n✅ All tests completed successfully!")
        print("🛠️ MCP tools are ready for integration with the chat interface!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_local_tools())
