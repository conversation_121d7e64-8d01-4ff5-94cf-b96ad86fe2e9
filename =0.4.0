Collecting cohere
  Downloading cohere-5.17.0-py3-none-any.whl.metadata (3.4 kB)
Collecting mistralai
  Downloading mistralai-1.9.3-py3-none-any.whl.metadata (37 kB)
Collecting groq
  Downloading groq-0.31.0-py3-none-any.whl.metadata (16 kB)
Collecting fastavro<2.0.0,>=1.9.4 (from cohere)
  Downloading fastavro-1.12.0-cp312-cp312-manylinux2014_x86_64.manylinux_2_17_x86_64.manylinux_2_28_x86_64.whl.metadata (5.7 kB)
Requirement already satisfied: httpx>=0.21.2 in /home/<USER>/.local/share/virtualenvs/LM-Studio-Voice-Conversation-GL9uXXcu/lib/python3.12/site-packages (from cohere) (0.28.1)
Collecting httpx-sse==0.4.0 (from cohere)
  Downloading httpx_sse-0.4.0-py3-none-any.whl.metadata (9.0 kB)
Requirement already satisfied: pydantic>=1.9.2 in /home/<USER>/.local/share/virtualenvs/LM-Studio-Voice-Conversation-GL9uXXcu/lib/python3.12/site-packages (from cohere) (2.11.3)
Requirement already satisfied: pydantic-core<3.0.0,>=2.18.2 in /home/<USER>/.local/share/virtualenvs/LM-Studio-Voice-Conversation-GL9uXXcu/lib/python3.12/site-packages (from cohere) (2.33.1)
Requirement already satisfied: requests<3.0.0,>=2.0.0 in /home/<USER>/.local/share/virtualenvs/LM-Studio-Voice-Conversation-GL9uXXcu/lib/python3.12/site-packages (from cohere) (2.32.3)
Collecting tokenizers<1,>=0.15 (from cohere)
  Using cached tokenizers-0.21.4-cp39-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (6.7 kB)
Collecting types-requests<3.0.0,>=2.0.0 (from cohere)
  Downloading types_requests-2.32.4.20250809-py3-none-any.whl.metadata (2.0 kB)
Requirement already satisfied: typing_extensions>=4.0.0 in /home/<USER>/.local/share/virtualenvs/LM-Studio-Voice-Conversation-GL9uXXcu/lib/python3.12/site-packages (from cohere) (4.12.2)
Collecting eval-type-backport>=0.2.0 (from mistralai)
  Downloading eval_type_backport-0.2.2-py3-none-any.whl.metadata (2.2 kB)
Requirement already satisfied: python-dateutil>=2.8.2 in /home/<USER>/.local/share/virtualenvs/LM-Studio-Voice-Conversation-GL9uXXcu/lib/python3.12/site-packages (from mistralai) (2.9.0.post0)
Requirement already satisfied: typing-inspection>=0.4.0 in /home/<USER>/.local/share/virtualenvs/LM-Studio-Voice-Conversation-GL9uXXcu/lib/python3.12/site-packages (from mistralai) (0.4.0)
Requirement already satisfied: anyio<5,>=3.5.0 in /home/<USER>/.local/share/virtualenvs/LM-Studio-Voice-Conversation-GL9uXXcu/lib/python3.12/site-packages (from groq) (3.7.1)
Requirement already satisfied: distro<2,>=1.7.0 in /home/<USER>/.local/share/virtualenvs/LM-Studio-Voice-Conversation-GL9uXXcu/lib/python3.12/site-packages (from groq) (1.9.0)
Requirement already satisfied: sniffio in /home/<USER>/.local/share/virtualenvs/LM-Studio-Voice-Conversation-GL9uXXcu/lib/python3.12/site-packages (from groq) (1.3.1)
Requirement already satisfied: idna>=2.8 in /home/<USER>/.local/share/virtualenvs/LM-Studio-Voice-Conversation-GL9uXXcu/lib/python3.12/site-packages (from anyio<5,>=3.5.0->groq) (3.10)
Requirement already satisfied: certifi in /home/<USER>/.local/share/virtualenvs/LM-Studio-Voice-Conversation-GL9uXXcu/lib/python3.12/site-packages (from httpx>=0.21.2->cohere) (2024.8.30)
Requirement already satisfied: httpcore==1.* in /home/<USER>/.local/share/virtualenvs/LM-Studio-Voice-Conversation-GL9uXXcu/lib/python3.12/site-packages (from httpx>=0.21.2->cohere) (1.0.9)
Requirement already satisfied: h11>=0.16 in /home/<USER>/.local/share/virtualenvs/LM-Studio-Voice-Conversation-GL9uXXcu/lib/python3.12/site-packages (from httpcore==1.*->httpx>=0.21.2->cohere) (0.16.0)
Requirement already satisfied: annotated-types>=0.6.0 in /home/<USER>/.local/share/virtualenvs/LM-Studio-Voice-Conversation-GL9uXXcu/lib/python3.12/site-packages (from pydantic>=1.9.2->cohere) (0.7.0)
Requirement already satisfied: six>=1.5 in /home/<USER>/.local/share/virtualenvs/LM-Studio-Voice-Conversation-GL9uXXcu/lib/python3.12/site-packages (from python-dateutil>=2.8.2->mistralai) (1.17.0)
Requirement already satisfied: charset-normalizer<4,>=2 in /home/<USER>/.local/share/virtualenvs/LM-Studio-Voice-Conversation-GL9uXXcu/lib/python3.12/site-packages (from requests<3.0.0,>=2.0.0->cohere) (3.4.1)
Requirement already satisfied: urllib3<3,>=1.21.1 in /home/<USER>/.local/share/virtualenvs/LM-Studio-Voice-Conversation-GL9uXXcu/lib/python3.12/site-packages (from requests<3.0.0,>=2.0.0->cohere) (2.4.0)
Collecting huggingface-hub<1.0,>=0.16.4 (from tokenizers<1,>=0.15->cohere)
  Downloading huggingface_hub-0.34.4-py3-none-any.whl.metadata (14 kB)
Collecting filelock (from huggingface-hub<1.0,>=0.16.4->tokenizers<1,>=0.15->cohere)
  Using cached filelock-3.18.0-py3-none-any.whl.metadata (2.9 kB)
Requirement already satisfied: fsspec>=2023.5.0 in /home/<USER>/.local/share/virtualenvs/LM-Studio-Voice-Conversation-GL9uXXcu/lib/python3.12/site-packages (from huggingface-hub<1.0,>=0.16.4->tokenizers<1,>=0.15->cohere) (2025.3.2)
Requirement already satisfied: packaging>=20.9 in /home/<USER>/.local/share/virtualenvs/LM-Studio-Voice-Conversation-GL9uXXcu/lib/python3.12/site-packages (from huggingface-hub<1.0,>=0.16.4->tokenizers<1,>=0.15->cohere) (25.0)
Requirement already satisfied: pyyaml>=5.1 in /home/<USER>/.local/share/virtualenvs/LM-Studio-Voice-Conversation-GL9uXXcu/lib/python3.12/site-packages (from huggingface-hub<1.0,>=0.16.4->tokenizers<1,>=0.15->cohere) (6.0.2)
Requirement already satisfied: tqdm>=4.42.1 in /home/<USER>/.local/share/virtualenvs/LM-Studio-Voice-Conversation-GL9uXXcu/lib/python3.12/site-packages (from huggingface-hub<1.0,>=0.16.4->tokenizers<1,>=0.15->cohere) (4.67.0)
Collecting hf-xet<2.0.0,>=1.1.3 (from huggingface-hub<1.0,>=0.16.4->tokenizers<1,>=0.15->cohere)
  Downloading hf_xet-1.1.7-cp37-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (703 bytes)
Downloading cohere-5.17.0-py3-none-any.whl (295 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 295.3/295.3 kB 5.3 MB/s eta 0:00:00
Downloading httpx_sse-0.4.0-py3-none-any.whl (7.8 kB)
Downloading mistralai-1.9.3-py3-none-any.whl (426 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 426.3/426.3 kB 9.6 MB/s eta 0:00:00
Downloading groq-0.31.0-py3-none-any.whl (131 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 131.4/131.4 kB 8.7 MB/s eta 0:00:00
Downloading eval_type_backport-0.2.2-py3-none-any.whl (5.8 kB)
Downloading fastavro-1.12.0-cp312-cp312-manylinux2014_x86_64.manylinux_2_17_x86_64.manylinux_2_28_x86_64.whl (3.5 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 3.5/3.5 MB 11.0 MB/s eta 0:00:00
Using cached tokenizers-0.21.4-cp39-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (3.1 MB)
Downloading types_requests-2.32.4.20250809-py3-none-any.whl (20 kB)
Downloading huggingface_hub-0.34.4-py3-none-any.whl (561 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 561.5/561.5 kB 10.8 MB/s eta 0:00:00
Downloading hf_xet-1.1.7-cp37-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (3.2 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 3.2/3.2 MB 10.9 MB/s eta 0:00:00
Using cached filelock-3.18.0-py3-none-any.whl (16 kB)
Installing collected packages: types-requests, httpx-sse, hf-xet, filelock, fastavro, eval-type-backport, huggingface-hub, tokenizers, mistralai, groq, cohere
Successfully installed cohere-5.17.0 eval-type-backport-0.2.2 fastavro-1.12.0 filelock-3.18.0 groq-0.31.0 hf-xet-1.1.7 httpx-sse-0.4.0 huggingface-hub-0.34.4 mistralai-1.9.3 tokenizers-0.21.4 types-requests-2.32.4.20250809
