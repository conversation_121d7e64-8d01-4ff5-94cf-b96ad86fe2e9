"""
Configuration management for the VoiceFlow AI Assistant.
Handles loading, validating, and accessing configuration settings.
"""
import os
import configparser
import logging
from pathlib import Path
from typing import Dict, Any, Optional, Union

logger = logging.getLogger(__name__)

def find_config_file(filename: str = 'config.ini', search_paths: Optional[list] = None) -> Optional[Path]:
    """Search for a configuration file in common locations.
    
    Args:
        filename: Name of the config file to find
        search_paths: Additional paths to search (appended to default paths)
        
    Returns:
        Path to the config file if found, None otherwise
    """
    if search_paths is None:
        search_paths = []
    
    # Default search paths (current directory, user config dir, etc.)
    default_paths = [
        Path.cwd(),
        Path.home() / '.config' / 'voiceflow-ai',
        Path('/etc/voiceflow-ai'),
        Path(__file__).parent.parent.parent,  # Project root
    ]
    
    # Combine and deduplicate paths
    all_paths = []
    seen = set()
    for path in default_paths + search_paths:
        path = Path(path).resolve()
        if str(path) not in seen:
            seen.add(str(path))
            all_paths.append(path)
    
    # Check each path for the config file
    for path in all_paths:
        config_path = path / filename
        if config_path.exists() and config_path.is_file():
            return config_path
    
    return None

def load_config(config_path: Optional[Union[str, Path]] = None) -> configparser.ConfigParser:
    """Load configuration from a file or use defaults.
    
    Args:
        config_path: Optional path to config file. If None, searches for config.ini
        
    Returns:
        ConfigParser object containing configuration settings
    """
    config = configparser.ConfigParser()
    
    # Set default configuration
    config.read_dict({
        'DEFAULT': {
            'app_name': 'VoiceFlow AI',
            'version': '0.1.0',
            'log_level': 'INFO',
            'max_file_size': '10',  # MB
        },
        'VOICE': {
            'wake_word': 'hey assistant',
            'energy_threshold': '4000',
            'pause_threshold': '0.8',
            'tts_rate': '150',
            'tts_volume': '0.9',
            'tts_voice': '0',
        },
        'OPENAI': {
            'api_key': '',
            'model': 'gpt-4-turbo-preview',
            'max_tokens': '1000',
            'temperature': '0.7',
        },
        'ANTHROPIC': {
            'api_key': '',
            'model': 'claude-3-opus-20240229',
            'max_tokens': '1000',
            'temperature': '0.7',
        },
        'GEMINI': {
            'api_key': '',
            'model': 'gemini-pro',
            'max_tokens': '1000',
            'temperature': '0.7',
        },
        'OLLAMA': {
            'base_url': 'http://localhost:11434',
            'model': 'llama2',
            'max_tokens': '1000',
            'temperature': '0.7',
        },
        'UI': {
            'theme': 'darkly',
            'width': '800',
            'height': '600',
        },
        'FILES': {
            'max_file_size': '10',  # MB
            'allowed_extensions': '.txt,.pdf,.docx,.jpg,.jpeg,.png',
        }
    })
    
    # If no config path provided, try to find one
    if config_path is None:
        config_path = find_config_file()
    
    # If we found a config file, read it
    if config_path:
        if isinstance(config_path, str):
            config_path = Path(config_path)
            
        if not config_path.exists():
            logger.warning(f"Config file not found: {config_path}")
        else:
            try:
                # Read the config file directly to ensure it's loaded
                with open(config_path, 'r') as f:
                    config.read_file(f)
                logger.info(f"Loaded configuration from {config_path}")
                
                # Debug: Print the loaded sections and options
                logger.debug(f"Loaded sections: {config.sections()}")
                for section in config.sections():
                    logger.debug(f"Section [{section}] options: {dict(config[section])}")
                
            except configparser.Error as e:
                logger.error(f"Error reading config file {config_path}: {e}")
            except Exception as e:
                logger.error(f"Unexpected error loading config from {config_path}: {e}")
    else:
        logger.warning("No configuration file found, using defaults")
    
    # Apply environment variable overrides
    _apply_env_overrides_to_config(config)
    
    return config

def _apply_env_overrides_to_config(config: configparser.ConfigParser) -> None:
    """Apply environment variable overrides to the config.
    
    Environment variables should be prefixed with VF_ and use double underscore for sections,
    e.g., VF_OPENAI__API_KEY=your_key
    """
    prefix = 'VF_'
    
    for key, value in os.environ.items():
        if not key.startswith(prefix):
            continue
            
        # Remove prefix and split into section and option
        key_parts = key[len(prefix):].split('__')
        if len(key_parts) < 2:
            logger.warning(f"Invalid environment variable format: {key}. Expected VF_SECTION__KEY=value")
            continue
            
        section = key_parts[0].upper()
        option = '__'.join(key_parts[1:]).lower()
        
        # Ensure section exists
        if not config.has_section(section) and section != 'DEFAULT':
            config.add_section(section)
            
        # Set the value
        config.set(section, option, value)
        logger.debug(f"Applied environment override: {section}.{option} = {value}")

def _apply_env_overrides(config: Dict[str, Any]) -> Dict[str, Any]:
    """Apply environment variable overrides to the config.
    
    Environment variables should be prefixed with VF_ and use double underscore for sections,
    e.g., VF_OPENAI__API_KEY=your_key
    """
    prefix = 'VF_'
    
    for key, value in os.environ.items():
        if not key.startswith(prefix):
            continue
            
        # Remove prefix and normalize key
        key = key[len(prefix):].lower()
        
        # Split into section and option
        if '__' in key:
            section, option = key.split('__', 1)
            section = section.upper()
            
            # Create section if it doesn't exist
            if section not in config:
                config[section] = {}
                
            # Set the value
            config[section][option] = value
    
    return config

def save_config(config: Dict[str, Any], config_path: Union[str, Path]) -> None:
    """Save configuration to a file.
    
    Args:
        config: Configuration dictionary
        config_path: Path to save the config file to
    """
    if isinstance(config_path, str):
        config_path = Path(config_path)
    
    # Create parent directories if they don't exist
    config_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Convert dictionary to ConfigParser
    parser = configparser.ConfigParser()
    
    # Add sections and options
    for section, options in config.items():
        if not isinstance(options, dict):
            continue
            
        parser[section] = {}
        for key, value in options.items():
            if isinstance(value, (str, int, float, bool)):
                parser[section][key] = str(value)
    
    # Write to file
    with open(config_path, 'w') as f:
        parser.write(f)
    
    logger.info(f"Configuration saved to {config_path}")

def get_log_level(level_str: str) -> int:
    """
    Convert log level string to logging constant.
    
    Args:
        level_str: String representation of the log level (e.g., 'DEBUG', 'INFO')
        
    Returns:
        int: The corresponding logging constant (defaults to logging.INFO if not found)
    """
    level_str = level_str.upper()
    levels = {
        'DEBUG': logging.DEBUG,
        'INFO': logging.INFO,
        'WARNING': logging.WARNING,
        'ERROR': logging.ERROR,
        'CRITICAL': logging.CRITICAL,
    }
    return levels.get(level_str, logging.INFO)
