"""
Logging configuration for the VoiceFlow AI Assistant.
Provides a centralized logging setup with file and console handlers.
"""
import logging
import sys
import os
from pathlib import Path
from typing import Optional, Dict, Any
from logging.handlers import RotatingFileHandler

# Default log format
DEFAULT_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
DEFAULT_DATE_FORMAT = '%Y-%m-%d %H:%M:%S'

# Log levels as strings to constants
LOG_LEVELS = {
    'DEBUG': logging.DEBUG,
    'INFO': logging.INFO,
    'WARNING': logging.WARNING,
    'ERROR': logging.ERROR,
    'CRITICAL': logging.CRITICAL,
}

def setup_logging(
    log_level: str = 'INFO',
    log_file: Optional[str] = None,
    log_format: str = DEFAULT_FORMAT,
    date_format: str = DEFAULT_DATE_FORMAT,
    max_bytes: int = 10 * 1024 * 1024,  # 10MB
    backup_count: int = 5,
) -> None:
    """Configure logging for the application.
    
    Args:
        log_level: Logging level as string (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: Path to the log file. If None, logs only to console.
        log_format: Log message format string
        date_format: Date format string for log messages
        max_bytes: Maximum log file size before rotation
        backup_count: Number of backup log files to keep
    """
    # Convert string log level to numeric value
    level = LOG_LEVELS.get(log_level.upper(), logging.INFO)
    
    # Create formatter
    formatter = logging.Formatter(log_format, datefmt=date_format)
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(level)
    
    # Clear existing handlers
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # Add console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)
    
    # Add file handler if log file is specified
    if log_file:
        try:
            # Create log directory if it doesn't exist
            log_path = Path(log_file).parent
            log_path.mkdir(parents=True, exist_ok=True)
            
            # Add rotating file handler
            file_handler = RotatingFileHandler(
                log_file,
                maxBytes=max_bytes,
                backupCount=backup_count,
                encoding='utf-8'
            )
            file_handler.setFormatter(formatter)
            root_logger.addHandler(file_handler)
            
            root_logger.info(f"Logging to file: {log_file}")
            
        except Exception as e:
            root_logger.error(f"Failed to set up file logging: {e}")
    
    # Set levels for specific loggers
    logging.getLogger('asyncio').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('httpx').setLevel(logging.WARNING)
    logging.getLogger('httpcore').setLevel(logging.WARNING)
    
    root_logger.info(f"Logging initialized at level {log_level}")

class LoggingContext:
    """Context manager for temporary logging configuration."""
    
    def __init__(
        self,
        logger: Optional[logging.Logger] = None,
        level: Optional[int] = None,
        handler: Optional[logging.Handler] = None,
        close: bool = True
    ):
        self.logger = logger
        self.level = level
        self.handler = handler
        self.close = close
        self.old_level = None
    
    def __enter__(self):
        if self.level is not None and self.logger is not None:
            self.old_level = self.logger.level
            self.logger.setLevel(self.level)
        if self.handler:
            if self.logger is not None:
                self.logger.addHandler(self.handler)
    
    def __exit__(self, et, ev, tb):
        if self.level is not None and self.logger is not None and self.old_level is not None:
            self.logger.setLevel(self.old_level)
        if self.handler:
            if self.logger is not None:
                self.logger.removeHandler(self.handler)
            if self.close:
                self.handler.close()

# Default logger for the utils module
logger = logging.getLogger(__name__)

def log_execution_time(logger: logging.Logger, message: str = "Execution time"):
    """Decorator to log the execution time of a function.
    
    Args:
        logger: Logger instance to use
        message: Custom message to prepend to the log
    """
    def decorator(func):
        import time
        from functools import wraps
        
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.monotonic()
            try:
                return await func(*args, **kwargs)
            finally:
                duration = time.monotonic() - start_time
                logger.debug(f"{message}: {func.__name__} took {duration:.4f} seconds")
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = time.monotonic()
            try:
                return func(*args, **kwargs)
            finally:
                duration = time.monotonic() - start_time
                logger.debug(f"{message}: {func.__name__} took {duration:.4f} seconds")
        
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        return sync_wrapper
    
    return decorator

def log_exceptions(logger: logging.Logger):
    """Decorator to log exceptions from a function.
    
    Args:
        logger: Logger instance to use
    """
    def decorator(func):
        from functools import wraps
        
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                logger.exception(f"Exception in {func.__name__}: {str(e)}")
                raise
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                logger.exception(f"Exception in {func.__name__}: {str(e)}")
                raise
        
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        return sync_wrapper
    
    return decorator
