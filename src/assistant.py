"""
Core assistant module that orchestrates voice processing, LLM interactions, and UI updates.
"""
import asyncio
import logging
from typing import Optional, Dict, Any, Union, List
from pathlib import Path
import configparser

from .llm.base import BaseLLM
from .voice.stt import SpeechToText
from .voice.tts import TextToSpeech
from .utils.config import load_config
from .utils.logger import setup_logging
from .mcp import MCPServerManager
from .mcp.function_caller import extract_tool_calls, format_tool_call

logger = logging.getLogger(__name__)

class VoiceAssistant:
    """Main assistant class that coordinates all components."""
    
    def __init__(self, config_path: Optional[Union[str, Path, configparser.ConfigParser]] = None):
        """Initialize the voice assistant with configuration.
        
        Args:
            config_path: Path to the configuration file or a ConfigParser object. 
                        If None, uses default configuration.
        """
        if config_path is not None and isinstance(config_path, configparser.ConfigParser):
            # Use the provided ConfigParser object directly
            self.config = config_path
        else:
            # Load configuration from file path or use defaults
            self.config = load_config(config_path)
        self.setup_done = False
        self.current_llm: Optional[BaseLLM] = None
        self.stt_engine: Optional[SpeechToText] = None
        self.tts_engine: Optional[TextToSpeech] = None
        self.conversation_history: list[dict] = []
        self.mcp_manager: Optional[MCPServerManager] = None
        
    async def initialize(self):
        """Initialize all components of the assistant."""
        if self.setup_done:
            return
            
        logger.info("Initializing Cherry Studio Clone Assistant...")
        
        # Initialize voice components if enabled
        voice_section = self.config['VOICE']
        enable_voice = voice_section.getboolean('enable_voice', False)
        
        if enable_voice:
            logger.info("Voice features are enabled. Initializing audio components...")
            try:
                self.stt_engine = SpeechToText(
                    wake_word=voice_section.get('wake_word', 'hey assistant'),
                    energy_threshold=voice_section.getint('energy_threshold', 4000),
                    pause_threshold=voice_section.getfloat('pause_threshold', 0.8),
                    dynamic_energy_threshold=voice_section.getboolean('dynamic_energy_threshold', True)
                )
                
                # Get TTS voice ID, defaulting to None if not set
                try:
                    voice_id = voice_section.getint('tts_voice')
                except (ValueError, KeyError):
                    voice_id = None
                    
                self.tts_engine = TextToSpeech(
                    rate=voice_section.getint('tts_rate', 150),
                    volume=voice_section.getfloat('tts_volume', 0.9),
                    voice_id=voice_id
                )
                logger.info("Audio components initialized successfully.")
            except Exception as e:
                logger.error(f"Failed to initialize audio components: {e}")
                logger.info("Continuing with voice features disabled.")
                self.stt_engine = None
                self.tts_engine = None
        else:
            logger.info("Voice features are disabled. Skipping audio component initialization.")
            self.stt_engine = None
            self.tts_engine = None
        
        # Initialize LLM with the configured provider (default to ollama if not specified)
        try:
            provider = self.config.get('LLM', 'provider', fallback='ollama')
            await self.set_llm_provider(provider)
        except Exception as e:
            logger.error(f"Error initializing LLM provider: {e}")
            logger.info("Falling back to Ollama provider")
            await self.set_llm_provider('ollama')
        
        # Initialize MCP manager
        try:
            logger.info("Initializing MCP manager...")
            self.mcp_manager = MCPServerManager()
            # Start auto-start servers
            await self.mcp_manager.start_all_servers()
            tools_count = len(self.mcp_manager.get_all_tools())
            logger.info(f"MCP manager initialized with {tools_count} available tools")
        except Exception as e:
            logger.error(f"Error initializing MCP manager: {e}")
            logger.info("Continuing without MCP support")
            self.mcp_manager = None
        
        self.setup_done = True
        logger.info("Assistant initialization complete")
    
    async def set_llm_provider(self, provider_name: str) -> bool:
        """Set the LLM provider to use for responses.
        
        Args:
            provider_name: Name of the LLM provider
            
        Returns:
            bool: True if provider was set successfully, False otherwise
        """
        provider_name = provider_name.lower()
        try:
            if provider_name == 'openai':
                from .llm.openai import OpenAIClient
                self.current_llm = OpenAIClient(self.config)
            elif provider_name == 'anthropic':
                from .llm.anthropic import AnthropicClient
                self.current_llm = AnthropicClient(self.config)
            elif provider_name == 'gemini':
                from .llm.gemini import GeminiClient
                self.current_llm = GeminiClient(self.config)
            elif provider_name == 'ollama':
                from .llm.ollama import OllamaClient
                self.current_llm = OllamaClient(self.config)
            elif provider_name == 'lmstudio':
                from .llm.lmstudio import LMStudioClient
                self.current_llm = LMStudioClient(self.config)
            elif provider_name == 'vllm':
                from .llm.vllm import VLLMClient
                self.current_llm = VLLMClient(self.config)
            elif provider_name == 'cohere':
                from .llm.cohere import CohereClient
                self.current_llm = CohereClient(self.config)
            elif provider_name == 'mistral':
                from .llm.mistral import MistralClient
                self.current_llm = MistralClient(self.config)
            elif provider_name == 'perplexity':
                from .llm.perplexity import PerplexityClient
                self.current_llm = PerplexityClient(self.config)
            elif provider_name == 'groq':
                from .llm.groq import GroqClient
                self.current_llm = GroqClient(self.config)
            else:
                logger.error(f"Unknown LLM provider: {provider_name}")
                return False
                
            logger.info(f"LLM provider set to: {provider_name}")
            return True
            
        except ImportError as e:
            logger.error(f"Failed to initialize {provider_name} provider: {e}")
            return False
    
    async def process_text_input(self, text: str) -> str:
        """Process text input and generate a response with MCP tool support.
        
        Args:
            text: User's input text
            
        Returns:
            str: Generated response from the LLM
        """
        request_id = id(self)  # Unique ID for this request
        logger.info(f"[Req {request_id}] Starting process_text_input with text: {text[:100]}...")
        
        if not self.current_llm:
            error_msg = "Error: No LLM provider is configured."
            logger.error(f"[Req {request_id}] {error_msg}")
            return error_msg
            
        try:
            # Add user message to conversation history
            self.conversation_history.append({"role": "user", "content": text})
            logger.info(f"[Req {request_id}] Added user message to history. Total messages: {len(self.conversation_history)}")
            
            # Check if the user is asking for tool usage or needs MCP tools
            if self.mcp_manager and self._should_use_tools(text):
                return await self._process_with_tools(text, request_id)
            else:
                return await self._process_without_tools(text, request_id)
                
        except Exception as e:
            error_msg = f"[Req {request_id}] Error in process_text_input: {e}"
            logger.error(error_msg, exc_info=True)
            return f"I encountered an error: {str(e)}"
    
    def _should_use_tools(self, text: str) -> bool:
        """Determine if the request should use MCP tools."""
        tool_keywords = [
            'read file', 'write file', 'create file', 'list directory', 'browse files',
            'web request', 'download', 'search web', 'calculate', 'math',
            'system info', 'current time', 'execute command', 'run command',
            'search text', 'encode', 'decode'
        ]
        
        text_lower = text.lower()
        return any(keyword in text_lower for keyword in tool_keywords)
    
    async def _process_with_tools(self, text: str, request_id: int) -> str:
        """Process text input with MCP tool support."""
        try:
            # Create enhanced messages with tool information
            enhanced_messages = self.conversation_history.copy()
            
            # Add system message about available tools
            if self.mcp_manager:
                tools = self.mcp_manager.get_all_tools()
                tools_info = self._create_tools_system_message(tools)
                
                # Insert tools info before the current conversation
                enhanced_messages.insert(-1, {
                    "role": "system", 
                    "content": tools_info
                })
            
            # Get response from LLM
            response = await self.current_llm.generate_response(messages=enhanced_messages)
            response_str = str(response.content if hasattr(response, 'content') else response)
            
            # Check if the response contains tool calls
            tool_calls = self._extract_tool_calls(response_str)
            
            if tool_calls:
                logger.info(f"[Req {request_id}] Found {len(tool_calls)} tool calls")
                final_response = await self._execute_tool_calls(tool_calls, response_str, request_id)
            else:
                final_response = response_str
            
            # Add assistant response to conversation history
            self.conversation_history.append({"role": "assistant", "content": final_response})
            logger.info(f"[Req {request_id}] Added assistant response to history")
            
            return final_response
            
        except Exception as e:
            logger.error(f"[Req {request_id}] Error in _process_with_tools: {e}")
            return f"I encountered an error while processing with tools: {str(e)}"
    
    async def _process_without_tools(self, text: str, request_id: int) -> str:
        """Process text input without tools (regular conversation)."""
        try:
            # Get response from LLM
            response = await self.current_llm.generate_response(messages=self.conversation_history)
            
            # Handle streaming response
            if hasattr(response, '__aiter__') and not isinstance(response, str):
                response_chunks = []
                async for chunk in response:
                    response_chunks.append(chunk)
                response_str = ''.join(response_chunks)
            else:
                response_str = str(response.content if hasattr(response, 'content') else response)
            
            # Add assistant response to conversation history
            self.conversation_history.append({"role": "assistant", "content": response_str})
            logger.info(f"[Req {request_id}] Regular conversation response processed")
            
            return response_str
            
        except Exception as e:
            logger.error(f"[Req {request_id}] Error in _process_without_tools: {e}")
            return f"I encountered an error: {str(e)}"
    
    def _create_tools_system_message(self, tools: List) -> str:
        """Create a system message describing available tools."""
        tools_info = "You have access to the following tools. Use them when appropriate to help the user:\n\n"
        
        for tool in tools[:10]:  # Limit to first 10 tools to avoid token overflow
            tools_info += f"🔧 {tool.name}: {tool.description}\n"
            if tool.parameters:
                params = ", ".join([f"{p.name} ({p.type.value})" for p in tool.parameters[:3]])
                tools_info += f"   Parameters: {params}\n"
            tools_info += "\n"
        
        tools_info += "To use a tool, include the tool call in your response like this:\n"
        tools_info += "TOOL_CALL: tool_name(param1='value1', param2='value2')\n\n"
        tools_info += "Always explain what you're doing when using tools."
        
        return tools_info
    
    def _extract_tool_calls(self, response: str) -> List[Dict[str, Any]]:
        """Extract tool calls from LLM response."""
        tool_calls = []
        lines = response.split('\n')
        
        for line in lines:
            if line.strip().startswith('TOOL_CALL:'):
                try:
                    tool_call_str = line.strip()[10:].strip()  # Remove 'TOOL_CALL:' prefix
                    
                    # Simple parsing for tool_name(param1='value1', param2='value2')
                    if '(' in tool_call_str and ')' in tool_call_str:
                        tool_name = tool_call_str.split('(')[0].strip()
                        params_str = tool_call_str.split('(', 1)[1].rsplit(')', 1)[0]
                        
                        # Parse parameters (simplified)
                        arguments = {}
                        if params_str.strip():
                            # This is a simplified parser - in production you'd want more robust parsing
                            import re
                            param_matches = re.findall(r"(\w+)=['\"]([^'\"]*)['\"]", params_str)
                            arguments = {match[0]: match[1] for match in param_matches}
                        
                        tool_calls.append({
                            'tool_name': tool_name,
                            'arguments': arguments
                        })
                        
                except Exception as e:
                    logger.error(f"Error parsing tool call: {e}")
                    continue
        
        return tool_calls
    
    async def _execute_tool_calls(self, tool_calls: List[Dict[str, Any]], original_response: str, request_id: int) -> str:
        """Execute tool calls and integrate results."""
        if not self.mcp_manager:
            return original_response
        
        tool_results = []
        
        for tool_call in tool_calls:
            try:
                tool_name = tool_call['tool_name']
                arguments = tool_call['arguments']
                
                logger.info(f"[Req {request_id}] Executing tool: {tool_name} with args: {arguments}")
                
                result = await self.mcp_manager.call_tool(tool_name, arguments)
                
                if result.is_error:
                    result_text = f"❌ Tool {tool_name} failed: {result.content[0].get('text', 'Unknown error')}"
                else:
                    result_text = f"✅ Tool {tool_name} result: {result.content[0].get('text', 'No result')}"
                
                tool_results.append(result_text)
                
            except Exception as e:
                logger.error(f"[Req {request_id}] Error executing tool {tool_call.get('tool_name', 'unknown')}: {e}")
                tool_results.append(f"❌ Tool execution error: {str(e)}")
        
        # Combine original response with tool results
        if tool_results:
            combined_response = original_response + "\n\n🛠️ Tool Results:\n" + "\n".join(tool_results)
        else:
            combined_response = original_response
        
        return combined_response
    
    async def call_mcp_tool(self, tool_name: str, arguments: Dict[str, Any]) -> str:
        """Directly call an MCP tool and return the result."""
        if not self.mcp_manager:
            return "MCP tools are not available"
        
        try:
            result = await self.mcp_manager.call_tool(tool_name, arguments)
            
            if result.is_error:
                return f"Tool error: {result.content[0].get('text', 'Unknown error')}"
            else:
                return result.content[0].get('text', 'No result')
                
        except Exception as e:
            logger.error(f"Error calling MCP tool {tool_name}: {e}")
            return f"Tool execution error: {str(e)}"
    
    def get_available_tools(self) -> List:
        """Get list of available MCP tools."""
        if not self.mcp_manager:
            return []
        return self.mcp_manager.get_all_tools()
    
    async def process_voice_input(self) -> str:
        """Process voice input and generate a spoken response.
        
        Returns:
            str: The generated response text
        """
        if not self.stt_engine or not self.tts_engine:
            return "Error: Voice components not initialized."
            
        try:
            # Listen for wake word
            logger.info("Listening for wake word...")
            await self.stt_engine.listen_for_wake_word()
            
            # Listen for voice command
            logger.info("Listening for command...")
            text = await self.stt_engine.listen()
            
            if not text:
                return "I didn't catch that. Could you please repeat?"
                
            # Process the text input
            response = await self.process_text_input(text)
            
            # Convert response to speech
            await self.tts_engine.speak(response)
            
            return response
            
        except Exception as e:
            logger.error(f"Error in voice processing: {e}")
            return f"I encountered an error: {str(e)}"
    
    async def process_document(self, file_path: str) -> str:
        """Process a document and return a summary or answer.
        
        Args:
            file_path: Path to the document file
            
        Returns:
            str: Summary or response about the document
        """
        try:
            from .utils.document_processor import process_document
            
            # Process the document
            doc_text = process_document(file_path)
            
            # Create a prompt for the LLM
            prompt = (
                f"Please summarize the following document in 3-5 key points. "
                f"Focus on the main ideas and important details.\n\n"
                f"Document content:\n{doc_text[:8000]}"  # Limit to first 8000 chars
            )
            
            # Get response from LLM and ensure it's a string
            response = await self.process_text_input(prompt)
            return str(response)
            
        except Exception as e:
            logger.error(f"Error processing document: {e}")
            return f"I couldn't process that document: {str(e)}"
    
    async def cleanup(self):
        """Clean up resources."""
        if self.mcp_manager:
            await self.mcp_manager.cleanup()
