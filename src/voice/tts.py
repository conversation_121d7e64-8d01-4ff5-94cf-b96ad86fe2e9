"""
Text-to-Speech (TTS) module for converting text to spoken audio.
Uses pyttsx3 for offline TTS with configurable voice, rate, and volume.
"""
import asyncio
import logging
import queue
import threading
from dataclasses import dataclass
from typing import Optional, Dict, List, Any, Callable, Awaitable

import pyttsx3
from pydantic import BaseModel

logger = logging.getLogger(__name__)

class VoiceInfo(BaseModel):
    """Information about an available voice."""
    id: str
    name: str
    languages: List[str]
    gender: Optional[str] = None
    age: Optional[int] = None

class TextToSpeech:
    """Handles text-to-speech synthesis with configurable voice and settings."""
    
    def __init__(self, 
                 rate: int = 150,
                 volume: float = 0.9,
                 voice_id: Optional[int] = None):
        """Initialize the TTS engine.
        
        Args:
            rate: Speech rate in words per minute
            volume: Volume level (0.0 to 1.0)
            voice_id: Index of the voice to use (from available_voices)
        """
        self.engine = self._init_engine()
        self.set_rate(rate)
        self.set_volume(volume)
        self.available_voices = self._get_available_voices()
        
        if voice_id is not None and 0 <= voice_id < len(self.available_voices):
            self.set_voice_by_id(voice_id)
        
        # Queue for speech synthesis tasks
        self.speech_queue = asyncio.Queue()
        self.is_speaking = False
        self.stop_speaking = threading.Event()
    
    def _init_engine(self) -> pyttsx3.Engine:
        """Initialize and configure the TTS engine."""
        try:
            engine = pyttsx3.init()
            # Configure engine properties
            engine.setProperty('rate', 150)
            engine.setProperty('volume', 0.9)
            return engine
        except Exception as e:
            logger.error(f"Failed to initialize TTS engine: {e}")
            raise
    
    def _get_available_voices(self) -> List[VoiceInfo]:
        """Get list of available voices with their properties."""
        voices = []
        for i, voice in enumerate(self.engine.getProperty('voices')):
            try:
                voice_info = VoiceInfo(
                    id=voice.id,
                    name=voice.name,
                    languages=voice.languages if hasattr(voice, 'languages') else [],
                    gender=getattr(voice, 'gender', None),
                    age=getattr(voice, 'age', None)
                )
                voices.append(voice_info)
                logger.debug(f"Voice {i}: {voice_info}")
            except Exception as e:
                logger.warning(f"Error processing voice {i}: {e}")
        
        if not voices:
            logger.warning("No voices found! TTS may not work properly.")
        
        return voices
    
    def set_voice_by_id(self, voice_id: int) -> bool:
        """Set the voice to use by index.
        
        Args:
            voice_id: Index of the voice in available_voices
            
        Returns:
            bool: True if voice was set successfully
        """
        if 0 <= voice_id < len(self.available_voices):
            voice = self.available_voices[voice_id]
            self.engine.setProperty('voice', voice.id)
            logger.info(f"Voice set to: {voice.name}")
            return True
        return False
    
    def set_rate(self, rate: int) -> None:
        """Set the speech rate in words per minute."""
        self.engine.setProperty('rate', max(50, min(rate, 300)))
    
    def set_volume(self, volume: float) -> None:
        """Set the volume level (0.0 to 1.0)."""
        self.engine.setProperty('volume', max(0.0, min(1.0, volume)))
    
    async def speak(self, text: str, wait: bool = True) -> None:
        """Convert text to speech and speak it.
        
        Args:
            text: The text to speak
            wait: If True, wait for speech to complete before returning
        """
        if not text.strip():
            return
            
        # Add to queue and process asynchronously
        await self.speech_queue.put(text)
        
        if wait:
            # Wait for the queue to be processed
            while not self.speech_queue.empty() or self.is_speaking:
                await asyncio.sleep(0.1)
    
    async def _process_speech_queue(self):
        """Process the speech queue in a background task."""
        while not self.stop_speaking.is_set():
            try:
                # Get text from queue with timeout to allow checking stop_speaking
                try:
                    text = await asyncio.wait_for(
                        self.speech_queue.get(),
                        timeout=0.1
                    )
                except asyncio.TimeoutError:
                    continue
                
                self.is_speaking = True
                
                # Run the blocking speak function in an executor
                loop = asyncio.get_event_loop()
                await loop.run_in_executor(
                    None,
                    lambda: self.engine.say(text)
                )
                
                # Run the blocking runAndWait in an executor
                await loop.run_in_executor(
                    None,
                    self.engine.runAndWait
                )
                
                self.speech_queue.task_done()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in speech queue: {e}")
            finally:
                self.is_speaking = False
    
    async def start(self):
        """Start the TTS service."""
        self.stop_speaking.clear()
        self.background_task = asyncio.create_task(self._process_speech_queue())
    
    async def stop(self):
        """Stop the TTS service and clear the queue."""
        self.stop_speaking.set()
        
        if hasattr(self, 'background_task'):
            self.background_task.cancel()
            try:
                await self.background_task
            except asyncio.CancelledError:
                pass
        
        # Clear the queue
        while not self.speech_queue.empty():
            try:
                self.speech_queue.get_nowait()
                self.speech_queue.task_done()
            except asyncio.QueueEmpty:
                break
    
    def __del__(self):
        """Clean up resources."""
        if hasattr(self, 'engine'):
            self.engine.stop()
            del self.engine
