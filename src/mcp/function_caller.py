"""
Utility for extracting and normalizing tool/function calls from LLM responses.

Supports:
- Simple TOOL_CALL: TOOL_CALL: tool_name(param='value', ...)
- JSON tool call: {"tool_call": {"name":"tool_name","arguments":{...}}}
- OpenAI-style function_call JSON embedded in assistant message

Provides helpers to parse and normalize calls into a list of dicts:
[{"tool_name": "...", "arguments": {...}}]
"""
import re
import json
import logging
from typing import List, Dict, Any, Optional

logger = logging.getLogger(__name__)

TOOL_CALL_RE = re.compile(r"TOOL_CALL:\s*([a-zA-Z0-9_\-]+)\s*\((.*?)\)\s*$")

def parse_tool_call_line(line: str) -> Optional[Dict[str, Any]]:
    """Parse a single TOOL_CALL line."""
    m = TOOL_CALL_RE.search(line.strip())
    if not m:
        return None
    tool_name = m.group(1)
    params_str = m.group(2).strip()
    arguments = {}
    if params_str:
        # find key='value' or key="value"
        param_matches = re.findall(r"(\w+)\s*=\s*['\"]([^'\"]*)['\"]", params_str)
        for k, v in param_matches:
            arguments[k] = v
    return {"tool_name": tool_name, "arguments": arguments}

def extract_from_text(response: str) -> List[Dict[str, Any]]:
    """Extract tool calls from plain text response."""
    calls = []
    for line in response.splitlines():
        parsed = parse_tool_call_line(line)
        if parsed:
            calls.append(parsed)
    return calls

def extract_from_json_block(response: str) -> List[Dict[str, Any]]:
    """
    Try to find JSON blocks in the response that include a tool call dict.
    Looks for {"tool_call": {...}} or {"function_call": {...}}
    """
    calls = []
    # find JSON-like substrings
    json_matches = re.findall(r"(\{.*\})", response, flags=re.DOTALL)
    for js in json_matches:
        try:
            data = json.loads(js)
        except Exception:
            continue
        # OpenAI-style function_call
        if isinstance(data, dict):
            if "tool_call" in data and isinstance(data["tool_call"], dict):
                tc = data["tool_call"]
                name = tc.get("name") or tc.get("tool")
                args = tc.get("arguments") or tc.get("args") or {}
                if name:
                    calls.append({"tool_name": name, "arguments": args})
            elif "function_call" in data and isinstance(data["function_call"], dict):
                fc = data["function_call"]
                name = fc.get("name")
                args = fc.get("arguments")
                if isinstance(args, str):
                    # sometimes arguments are a JSON string
                    try:
                        args = json.loads(args)
                    except Exception:
                        args = {}
                if name:
                    calls.append({"tool_name": name, "arguments": args or {}})
    return calls

def extract_tool_calls(response: str) -> List[Dict[str, Any]]:
    """Extract tool calls from an LLM response using multiple strategies."""
    calls = []
    # 1) JSON blocks
    try:
        calls.extend(extract_from_json_block(response))
    except Exception as e:
        logger.debug(f"extract_from_json_block error: {e}")
    # 2) Plain TOOL_CALL lines
    try:
        calls.extend(extract_from_text(response))
    except Exception as e:
        logger.debug(f"extract_from_text error: {e}")
    # deduplicate by tool_name+arguments string
    seen = set()
    unique_calls = []
    for c in calls:
        key = f"{c.get('tool_name')}::{json.dumps(c.get('arguments', {}), sort_keys=True)}"
        if key not in seen:
            seen.add(key)
            unique_calls.append(c)
    return unique_calls

def format_tool_call(tool_name: str, arguments: Dict[str, Any]) -> str:
    """Return a standardized TOOL_CALL string."""
    args_parts = []
    for k, v in arguments.items():
        # stringify values
        safe_v = str(v).replace("'", "\\'")
        args_parts.append(f"{k}='{safe_v}'")
    return f"TOOL_CALL: {tool_name}({', '.join(args_parts)})"
