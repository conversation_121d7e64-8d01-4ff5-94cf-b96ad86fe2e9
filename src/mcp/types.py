"""
MCP (Model Context Protocol) type definitions and data structures.
"""
from dataclasses import dataclass, field
from typing import Dict, List, Any, Optional, Union
from enum import Enum
import json

class MCPTransport(Enum):
    """MCP transport types."""
    STDIO = "stdio"
    HTTP = "http"
    WEBSOCKET = "websocket"

class MCPToolInputType(Enum):
    """MCP tool input parameter types."""
    STRING = "string"
    NUMBER = "number"
    INTEGER = "integer"
    BOOLEAN = "boolean"
    ARRAY = "array"
    OBJECT = "object"

@dataclass
class MCPToolParameter:
    """MCP tool parameter definition."""
    name: str
    type: MCPToolInputType
    description: str
    required: bool = False
    default: Any = None
    enum: Optional[List[Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        result = {
            "type": self.type.value,
            "description": self.description
        }
        if self.default is not None:
            result["default"] = self.default
        if self.enum is not None:
            result["enum"] = self.enum
        return result

@dataclass
class MCPTool:
    """MCP tool definition."""
    name: str
    description: str
    parameters: List[MCPToolParameter] = field(default_factory=list)
    server_name: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "name": self.name,
            "description": self.description,
            "inputSchema": {
                "type": "object",
                "properties": {
                    param.name: param.to_dict() for param in self.parameters
                },
                "required": [param.name for param in self.parameters if param.required]
            }
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any], server_name: str = "") -> 'MCPTool':
        """Create MCPTool from dictionary."""
        parameters = []
        
        if "inputSchema" in data and "properties" in data["inputSchema"]:
            required_fields = data["inputSchema"].get("required", [])
            
            for param_name, param_data in data["inputSchema"]["properties"].items():
                param_type = MCPToolInputType(param_data.get("type", "string"))
                parameters.append(MCPToolParameter(
                    name=param_name,
                    type=param_type,
                    description=param_data.get("description", ""),
                    required=param_name in required_fields,
                    default=param_data.get("default"),
                    enum=param_data.get("enum")
                ))
        
        return cls(
            name=data["name"],
            description=data["description"],
            parameters=parameters,
            server_name=server_name
        )

@dataclass
class MCPResource:
    """MCP resource definition."""
    uri: str
    name: str
    description: str
    mime_type: Optional[str] = None
    server_name: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        result = {
            "uri": self.uri,
            "name": self.name,
            "description": self.description
        }
        if self.mime_type:
            result["mimeType"] = self.mime_type
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any], server_name: str = "") -> 'MCPResource':
        """Create MCPResource from dictionary."""
        return cls(
            uri=data["uri"],
            name=data["name"],
            description=data["description"],
            mime_type=data.get("mimeType"),
            server_name=server_name
        )

@dataclass
class MCPServer:
    """MCP server configuration."""
    name: str
    display_name: str
    description: str
    transport: MCPTransport
    command: Optional[List[str]] = None
    args: Optional[List[str]] = None
    env: Optional[Dict[str, str]] = None
    url: Optional[str] = None
    enabled: bool = True
    auto_start: bool = True
    tools: List[MCPTool] = field(default_factory=list)
    resources: List[MCPResource] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        result = {
            "name": self.name,
            "display_name": self.display_name,
            "description": self.description,
            "transport": self.transport.value,
            "enabled": self.enabled,
            "auto_start": self.auto_start
        }
        
        if self.command:
            result["command"] = self.command
        if self.args:
            result["args"] = self.args
        if self.env:
            result["env"] = self.env
        if self.url:
            result["url"] = self.url
        
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MCPServer':
        """Create MCPServer from dictionary."""
        transport = MCPTransport(data.get("transport", "stdio"))
        
        return cls(
            name=data["name"],
            display_name=data.get("display_name", data["name"]),
            description=data.get("description", ""),
            transport=transport,
            command=data.get("command"),
            args=data.get("args"),
            env=data.get("env"),
            url=data.get("url"),
            enabled=data.get("enabled", True),
            auto_start=data.get("auto_start", True)
        )

@dataclass
class MCPToolCall:
    """MCP tool call request."""
    tool_name: str
    arguments: Dict[str, Any]
    call_id: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "method": "tools/call",
            "params": {
                "name": self.tool_name,
                "arguments": self.arguments
            }
        }

@dataclass
class MCPToolResult:
    """MCP tool call result."""
    call_id: str
    content: List[Dict[str, Any]]
    is_error: bool = False
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any], call_id: str = "") -> 'MCPToolResult':
        """Create MCPToolResult from dictionary."""
        if "error" in data:
            return cls(
                call_id=call_id,
                content=[{"type": "text", "text": f"Error: {data['error']}"}],
                is_error=True
            )
        else:
            content = data.get("content", [])
            return cls(
                call_id=call_id,
                content=content,
                is_error=False
            )

@dataclass
class MCPResourceContent:
    """MCP resource content."""
    uri: str
    content: str
    mime_type: Optional[str] = None
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MCPResourceContent':
        """Create MCPResourceContent from dictionary."""
        contents = data.get("contents", [])
        if contents:
            first_content = contents[0]
            return cls(
                uri=first_content.get("uri", ""),
                content=first_content.get("text", ""),
                mime_type=first_content.get("mimeType")
            )
        else:
            return cls(uri="", content="")
