"""
Local MCP tools server providing built-in tools for Cherry Studio Clone.
"""
import asyncio
import json
import logging
import os
import subprocess
import sys
import sqlite3
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
import requests
import platform

from .types import MCPTool, MCPToolParameter, MCPToolInputType, MCPToolResult

logger = logging.getLogger(__name__)


class LocalToolsServer:
    """Local MCP server providing built-in tools."""

    def __init__(self):
        self.name = "local_tools"
        self.display_name = "Local Tools"
        self.description = "Built-in tools for file operations, web requests, and system utilities"
        self.tools = self._initialize_tools()

    def _initialize_tools(self) -> List[MCPTool]:
        """Initialize the built-in tools."""
        tools: List[MCPTool] = []

        # File operations
        tools.append(
            MCPTool(
                name="read_file",
                description="Read the contents of a file",
                parameters=[MCPToolParameter("path", MCPToolInputType.STRING, "Path to the file to read", required=True)],
                server_name=self.name,
            )
        )

        tools.append(
            MCPTool(
                name="write_file",
                description="Write content to a file",
                parameters=[
                    MCPToolParameter("path", MCPToolInputType.STRING, "Path to the file to write", required=True),
                    MCPToolParameter("content", MCPToolInputType.STRING, "Content to write to the file", required=True),
                    MCPToolParameter("append", MCPToolInputType.BOOLEAN, "Whether to append to the file", default=False),
                ],
                server_name=self.name,
            )
        )

        tools.append(
            MCPTool(
                name="list_directory",
                description="List files and directories in a path",
                parameters=[
                    MCPToolParameter("path", MCPToolInputType.STRING, "Directory path to list", required=True),
                    MCPToolParameter("recursive", MCPToolInputType.BOOLEAN, "Whether to list recursively", default=False),
                ],
                server_name=self.name,
            )
        )

        tools.append(
            MCPTool(
                name="create_directory",
                description="Create a new directory",
                parameters=[
                    MCPToolParameter("path", MCPToolInputType.STRING, "Directory path to create", required=True),
                    MCPToolParameter("parents", MCPToolInputType.BOOLEAN, "Create parent directories if needed", default=True),
                ],
                server_name=self.name,
            )
        )

        # Web requests
        tools.append(
            MCPTool(
                name="web_request",
                description="Make an HTTP request to a URL",
                parameters=[
                    MCPToolParameter("url", MCPToolInputType.STRING, "URL to request", required=True),
                    MCPToolParameter("method", MCPToolInputType.STRING, "HTTP method", default="GET", enum=["GET", "POST", "PUT", "DELETE", "PATCH"]),
                    MCPToolParameter("headers", MCPToolInputType.OBJECT, "HTTP headers", default={}),
                    MCPToolParameter("data", MCPToolInputType.OBJECT, "Request body data", default={}),
                ],
                server_name=self.name,
            )
        )

        tools.append(
            MCPTool(
                name="download_file",
                description="Download a file from a URL",
                parameters=[
                    MCPToolParameter("url", MCPToolInputType.STRING, "URL to download from", required=True),
                    MCPToolParameter("path", MCPToolInputType.STRING, "Local path to save the file", required=True),
                ],
                server_name=self.name,
            )
        )

        # System utilities
        tools.append(
            MCPTool(
                name="execute_command",
                description="Execute a system command",
                parameters=[
                    MCPToolParameter("command", MCPToolInputType.STRING, "Command to execute", required=True),
                    MCPToolParameter("working_dir", MCPToolInputType.STRING, "Working directory", default=""),
                    MCPToolParameter("timeout", MCPToolInputType.NUMBER, "Timeout in seconds", default=30),
                ],
                server_name=self.name,
            )
        )

        tools.append(MCPTool(name="get_system_info", description="Get system information", parameters=[], server_name=self.name))

        tools.append(
            MCPTool(
                name="get_current_time",
                description="Get the current date and time",
                parameters=[MCPToolParameter("format", MCPToolInputType.STRING, "Time format string", default="%Y-%m-%d %H:%M:%S")],
                server_name=self.name,
            )
        )

        # Text processing
        tools.append(
            MCPTool(
                name="search_text",
                description="Search for text patterns in a string",
                parameters=[
                    MCPToolParameter("text", MCPToolInputType.STRING, "Text to search in", required=True),
                    MCPToolParameter("pattern", MCPToolInputType.STRING, "Search pattern (regex)", required=True),
                    MCPToolParameter("case_sensitive", MCPToolInputType.BOOLEAN, "Case sensitive search", default=False),
                ],
                server_name=self.name,
            )
        )

        tools.append(
            MCPTool(
                name="encode_decode",
                description="Encode or decode text using various methods",
                parameters=[
                    MCPToolParameter(
                        "text",
                        MCPToolInputType.STRING,
                        "Text to encode/decode",
                        required=True,
                    ),
                    MCPToolParameter(
                        "method",
                        MCPToolInputType.STRING,
                        "Encoding method",
                        required=True,
                        enum=["base64_encode", "base64_decode", "url_encode", "url_decode", "html_encode", "html_decode"],
                    ),
                ],
                server_name=self.name,
            )
        )

        # Math and calculations
        tools.append(
            MCPTool(
                name="calculate",
                description="Perform mathematical calculations",
                parameters=[MCPToolParameter("expression", MCPToolInputType.STRING, "Mathematical expression to evaluate", required=True)],
                server_name=self.name,
            )
        )

        # Database tools (SQLite)
        tools.append(
            MCPTool(
                name="sqlite_query",
                description="Execute a read-only SQL query against a local SQLite database file",
                parameters=[
                    MCPToolParameter("db_path", MCPToolInputType.STRING, "Path to the SQLite database file", required=True),
                    MCPToolParameter("query", MCPToolInputType.STRING, "SQL query to execute (SELECT only)", required=True),
                    MCPToolParameter("max_rows", MCPToolInputType.INTEGER, "Maximum rows to return", default=100),
                ],
                server_name=self.name,
            )
        )

        # Image utilities (simple)
        tools.append(
            MCPTool(
                name="image_info",
                description="Return basic image metadata (format, size) for a local image",
                parameters=[MCPToolParameter("path", MCPToolInputType.STRING, "Path to the image file", required=True)],
                server_name=self.name,
            )
        )

        # Code analysis (basic)
        tools.append(
            MCPTool(
                name="lint_python",
                description="Run a basic lint check (flake8) on a python file and return the output",
                parameters=[MCPToolParameter("path", MCPToolInputType.STRING, "Path to the python file", required=True)],
                server_name=self.name,
            )
        )

        return tools

    def get_tools(self) -> List[MCPTool]:
        """Get all available tools."""
        return self.tools

    def get_tool(self, tool_name: str) -> Optional[MCPTool]:
        """Get a specific tool by name."""
        for tool in self.tools:
            if tool.name == tool_name:
                return tool
        return None

    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> MCPToolResult:
        """Call a tool with the given arguments using dynamic dispatch."""
        try:
            handler_name = f"_{tool_name}"
            if hasattr(self, handler_name):
                handler = getattr(self, handler_name)
                return await handler(arguments)
            else:
                return MCPToolResult(call_id="", content=[{"type": "text", "text": f"Unknown tool: {tool_name}"}], is_error=True)
        except Exception as e:
            logger.error(f"Error calling tool {tool_name}: {e}", exc_info=True)
            return MCPToolResult(call_id="", content=[{"type": "text", "text": f"Tool error: {str(e)}"}], is_error=True)

    async def _read_file(self, args: Dict[str, Any]) -> MCPToolResult:
        """Read file tool implementation."""
        path = Path(args["path"])

        if not path.exists():
            return MCPToolResult("", [{"type": "text", "text": f"File not found: {path}"}], True)

        if not path.is_file():
            return MCPToolResult("", [{"type": "text", "text": f"Path is not a file: {path}"}], True)

        try:
            with open(path, "r", encoding="utf-8") as f:
                content = f.read()

            return MCPToolResult("", [{"type": "text", "text": content}])
        except Exception as e:
            return MCPToolResult("", [{"type": "text", "text": f"Error reading file: {e}"}], True)

    async def _write_file(self, args: Dict[str, Any]) -> MCPToolResult:
        """Write file tool implementation."""
        path = Path(args["path"])
        content = args["content"]
        append = args.get("append", False)

        try:
            # Create parent directories if needed
            path.parent.mkdir(parents=True, exist_ok=True)

            mode = "a" if append else "w"
            with open(path, mode, encoding="utf-8") as f:
                f.write(content)

            action = "appended to" if append else "written to"
            return MCPToolResult("", [{"type": "text", "text": f"Content {action} {path}"}])
        except Exception as e:
            return MCPToolResult("", [{"type": "text", "text": f"Error writing file: {e}"}], True)

    async def _list_directory(self, args: Dict[str, Any]) -> MCPToolResult:
        """List directory tool implementation."""
        path = Path(args["path"])
        recursive = args.get("recursive", False)

        if not path.exists():
            return MCPToolResult("", [{"type": "text", "text": f"Directory not found: {path}"}], True)

        if not path.is_dir():
            return MCPToolResult("", [{"type": "text", "text": f"Path is not a directory: {path}"}], True)

        try:
            items: List[str] = []

            if recursive:
                for item in path.rglob("*"):
                    item_type = "📁" if item.is_dir() else "📄"
                    relative_path = item.relative_to(path)
                    items.append(f"{item_type} {relative_path}")
            else:
                for item in path.iterdir():
                    item_type = "📁" if item.is_dir() else "📄"
                    items.append(f"{item_type} {item.name}")

            items.sort()
            result_text = f"Contents of {path}:\n" + "\n".join(items)

            return MCPToolResult("", [{"type": "text", "text": result_text}])
        except Exception as e:
            return MCPToolResult("", [{"type": "text", "text": f"Error listing directory: {e}"}], True)

    async def _create_directory(self, args: Dict[str, Any]) -> MCPToolResult:
        """Create directory tool implementation."""
        path = Path(args["path"])
        parents = args.get("parents", True)

        try:
            path.mkdir(parents=parents, exist_ok=True)
            return MCPToolResult("", [{"type": "text", "text": f"Directory created: {path}"}])
        except Exception as e:
            return MCPToolResult("", [{"type": "text", "text": f"Error creating directory: {e}"}], True)

    async def _web_request(self, args: Dict[str, Any]) -> MCPToolResult:
        """Web request tool implementation."""
        url = args["url"]
        method = args.get("method", "GET").upper()
        headers = args.get("headers", {})
        data = args.get("data", {})

        try:
            response = requests.request(method=method, url=url, headers=headers, json=data if data else None, timeout=30)

            result_text = f"HTTP {response.status_code} {response.reason}\n"
            result_text += f"URL: {url}\n"
            result_text += f"Headers: {dict(response.headers)}\n\n"
            result_text += f"Response:\n{response.text[:2000]}{'...' if len(response.text) > 2000 else ''}"

            return MCPToolResult("", [{"type": "text", "text": result_text}])
        except Exception as e:
            return MCPToolResult("", [{"type": "text", "text": f"Web request error: {e}"}], True)

    async def _download_file(self, args: Dict[str, Any]) -> MCPToolResult:
        """Download file tool implementation."""
        url = args["url"]
        path = Path(args["path"])

        try:
            # Create parent directories if needed
            path.parent.mkdir(parents=True, exist_ok=True)

            response = requests.get(url, timeout=60, stream=True)
            response.raise_for_status()

            with open(path, "wb") as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)

            file_size = path.stat().st_size
            return MCPToolResult("", [{"type": "text", "text": f"Downloaded {url} to {path} ({file_size} bytes)"}])
        except Exception as e:
            return MCPToolResult("", [{"type": "text", "text": f"Download error: {e}"}], True)

    async def _execute_command(self, args: Dict[str, Any]) -> MCPToolResult:
        """Execute command tool implementation."""
        command = args["command"]
        working_dir = args.get("working_dir", "")
        timeout = args.get("timeout", 30)

        try:
            # Security check - only allow safe commands
            dangerous_commands = ["rm -rf", "del /f", "format", "fdisk", "mkfs"]
            if any(dangerous in command.lower() for dangerous in dangerous_commands):
                return MCPToolResult("", [{"type": "text", "text": "Command blocked for security reasons"}], True)

            # Execute the command
            result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=timeout, cwd=working_dir if working_dir else None)

            output = f"Command: {command}\n"
            output += f"Exit Code: {result.returncode}\n"
            output += f"Working Directory: {working_dir or os.getcwd()}\n\n"

            if result.stdout:
                output += f"STDOUT:\n{result.stdout}\n"

            if result.stderr:
                output += f"STDERR:\n{result.stderr}\n"

            return MCPToolResult("", [{"type": "text", "text": output}])
        except subprocess.TimeoutExpired:
            return MCPToolResult("", [{"type": "text", "text": f"Command timed out after {timeout} seconds"}], True)
        except Exception as e:
            return MCPToolResult("", [{"type": "text", "text": f"Command execution error: {e}"}], True)

    async def _get_system_info(self, args: Dict[str, Any]) -> MCPToolResult:
        """Get system info tool implementation."""
        try:
            info = {
                "platform": platform.platform(),
                "system": platform.system(),
                "release": platform.release(),
                "version": platform.version(),
                "machine": platform.machine(),
                "processor": platform.processor(),
                "python_version": sys.version,
                "current_directory": os.getcwd(),
                "user": os.getenv("USER") or os.getenv("USERNAME") or "unknown",
            }

            result_text = "🖥️ System Information:\n"
            for key, value in info.items():
                result_text += f"{key.replace('_', ' ').title()}: {value}\n"

            return MCPToolResult("", [{"type": "text", "text": result_text}])
        except Exception as e:
            return MCPToolResult("", [{"type": "text", "text": f"System info error: {e}"}], True)

    async def _get_current_time(self, args: Dict[str, Any]) -> MCPToolResult:
        """Get current time tool implementation."""
        try:
            time_format = args.get("format", "%Y-%m-%d %H:%M:%S")
            current_time = datetime.now().strftime(time_format)

            return MCPToolResult("", [{"type": "text", "text": f"Current time: {current_time}"}])
        except Exception as e:
            return MCPToolResult("", [{"type": "text", "text": f"Time error: {e}"}], True)

    async def _search_text(self, args: Dict[str, Any]) -> MCPToolResult:
        """Search text tool implementation."""
        import re

        try:
            text = args["text"]
            pattern = args["pattern"]
            case_sensitive = args.get("case_sensitive", False)

            flags = 0 if case_sensitive else re.IGNORECASE
            matches = re.finditer(pattern, text, flags)

            results: List[str] = []
            for i, match in enumerate(matches, 1):
                start, end = match.span()
                matched_text = match.group()
                context_start = max(0, start - 50)
                context_end = min(len(text), end + 50)
                context = text[context_start:context_end]

                results.append(f"Match {i}: '{matched_text}' at position {start}-{end}")
                results.append(f"Context: ...{context}...")
                results.append("")

            if not results:
                result_text = f"No matches found for pattern: {pattern}"
            else:
                result_text = f"Found {len(results)//3} matches for pattern: {pattern}\n\n" + "\n".join(results)

            return MCPToolResult("", [{"type": "text", "text": result_text}])
        except Exception as e:
            return MCPToolResult("", [{"type": "text", "text": f"Search error: {e}"}], True)

    async def _encode_decode(self, args: Dict[str, Any]) -> MCPToolResult:
        """Encode/decode tool implementation."""
        import base64
        import urllib.parse
        import html

        try:
            text = args["text"]
            method = args["method"]

            if method == "base64_encode":
                result = base64.b64encode(text.encode("utf-8")).decode("utf-8")
            elif method == "base64_decode":
                result = base64.b64decode(text.encode("utf-8")).decode("utf-8")
            elif method == "url_encode":
                result = urllib.parse.quote(text)
            elif method == "url_decode":
                result = urllib.parse.unquote(text)
            elif method == "html_encode":
                result = html.escape(text)
            elif method == "html_decode":
                result = html.unescape(text)
            else:
                return MCPToolResult("", [{"type": "text", "text": f"Unknown encoding method: {method}"}], True)

            return MCPToolResult("", [{"type": "text", "text": f"Result: {result}"}])
        except Exception as e:
            return MCPToolResult("", [{"type": "text", "text": f"Encoding error: {e}"}], True)

    async def _calculate(self, args: Dict[str, Any]) -> MCPToolResult:
        """Calculate tool implementation."""
        try:
            expression = args["expression"]

            # Security check - only allow safe mathematical operations
            allowed_chars = set("0123456789+-*/().% abcdefghijklmnopqrstuvwxyz")
            if not all(c.lower() in allowed_chars for c in expression):
                return MCPToolResult("", [{"type": "text", "text": "Expression contains unsafe characters"}], True)

            # Safe evaluation using eval with restricted globals
            safe_dict = {
                "__builtins__": {},
                "abs": abs,
                "round": round,
                "min": min,
                "max": max,
                "sum": sum,
                "pow": pow,
                "sqrt": lambda x: x ** 0.5,
                "pi": 3.14159265359,
                "e": 2.71828182846,
            }

            result = eval(expression, safe_dict)
            return MCPToolResult("", [{"type": "text", "text": f"Result: {result}"}])
        except Exception as e:
            return MCPToolResult("", [{"type": "text", "text": f"Calculation error: {e}"}], True)

    async def _sqlite_query(self, args: Dict[str, Any]) -> MCPToolResult:
        """Execute a read-only SELECT query against a local SQLite database file."""
        try:
            db_path = Path(args.get("db_path", ""))
            query = args.get("query", "")
            max_rows = int(args.get("max_rows", 100))

            if not db_path.exists():
                return MCPToolResult("", [{"type": "text", "text": f"Database not found: {db_path}"}], True)

            if not query.strip().lower().startswith("select"):
                return MCPToolResult("", [{"type": "text", "text": "Only SELECT queries are allowed"}], True)

            conn = sqlite3.connect(str(db_path))
            conn.row_factory = sqlite3.Row
            cur = conn.cursor()
            cur.execute(query)
            rows = cur.fetchmany(max_rows)
            columns = rows[0].keys() if rows else []
            result_lines: List[str] = []
            header = " | ".join(columns)
            result_lines.append(header)
            result_lines.append("-" * len(header))
            for r in rows:
                result_lines.append(" | ".join(str(r[c]) for c in columns))
            conn.close()
            return MCPToolResult("", [{"type": "text", "text": "\n".join(result_lines)}])
        except Exception as e:
            return MCPToolResult("", [{"type": "text", "text": f"SQLite query error: {e}"}], True)

    async def _image_info(self, args: Dict[str, Any]) -> MCPToolResult:
        """Return basic image metadata for a local image file."""
        try:
            from PIL import Image
        except Exception as e:
            return MCPToolResult("", [{"type": "text", "text": f"Pillow not available: {e}"}], True)

        path = Path(args.get("path", ""))
        if not path.exists() or not path.is_file():
            return MCPToolResult("", [{"type": "text", "text": f"Image not found: {path}"}], True)

        try:
            with Image.open(path) as img:
                info = {"format": img.format, "mode": img.mode, "size": img.size}
            text = f"Image: {path}\\nFormat: {info['format']}\\nMode: {info['mode']}\\nSize: {info['size']}"
            return MCPToolResult("", [{"type": "text", "text": text}])
        except Exception as e:
            return MCPToolResult("", [{"type": "text", "text": f"Image info error: {e}"}], True)

    async def _lint_python(self, args: Dict[str, Any]) -> MCPToolResult:
        """Run a simple flake8 lint on a python file and return output."""
        path = Path(args.get("path", ""))
        if not path.exists() or not path.is_file():
            return MCPToolResult("", [{"type": "text", "text": f"File not found: {path}"}], True)

        try:
            # Run flake8 as a subprocess
            result = subprocess.run([sys.executable, "-m", "flake8", str(path)], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, timeout=30)
            output = result.stdout.strip() or result.stderr.strip() or "No lint issues found"
            return MCPToolResult("", [{"type": "text", "text": output}])
        except Exception as e:
            return MCPToolResult("", [{"type": "text", "text": f"Lint error: {e}"}], True)
