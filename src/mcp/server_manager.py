"""
MCP Server Manager for managing multiple MCP servers and their tools.
"""
import asyncio
import json
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any
import uuid

from .types import MCPServer, MCPTool, MCPResource, MCPTransport, MCPToolResult
from .client import <PERSON><PERSON><PERSON>
from .local_tools import LocalToolsServer
from .discovery import probe_mcp_server_auto, summarize_tools

logger = logging.getLogger(__name__)

class MCPServerManager:
    """Manages multiple MCP servers and provides unified tool access."""
    
    def __init__(self, data_dir: str = "data"):
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(exist_ok=True)
        self.servers_file = self.data_dir / "mcp_servers.json"
        
        self.servers: Dict[str, MCPServer] = {}
        self.clients: Dict[str, MCPClient] = {}
        self.local_tools_server = LocalToolsServer()
        
        # Load server configurations
        self.load_servers()
        
        # Create default servers if none exist
        if not self.servers:
            self.create_default_servers()
    
    def load_servers(self):
        """Load MCP server configurations from file."""
        try:
            if self.servers_file.exists():
                with open(self.servers_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.servers = {
                        name: MCPServer.from_dict(server_data)
                        for name, server_data in data.items()
                    }
                logger.info(f"Loaded {len(self.servers)} MCP server configurations")
            else:
                self.servers = {}
                logger.info("No MCP servers file found, starting fresh")
        except Exception as e:
            logger.error(f"Error loading MCP servers: {e}")
            self.servers = {}
    
    def save_servers(self):
        """Save MCP server configurations to file."""
        try:
            with open(self.servers_file, 'w', encoding='utf-8') as f:
                data = {
                    name: server.to_dict()
                    for name, server in self.servers.items()
                }
                json.dump(data, f, indent=2, ensure_ascii=False)
            logger.debug(f"Saved {len(self.servers)} MCP server configurations")
        except Exception as e:
            logger.error(f"Error saving MCP servers: {e}")
    
    def create_default_servers(self):
        """Create default MCP server configurations."""
        # Example MCP servers that users might want to add
        default_servers = [
            {
                "name": "filesystem",
                "display_name": "File System Tools",
                "description": "Tools for file and directory operations",
                "transport": "stdio",
                "command": ["npx", "-y", "@modelcontextprotocol/server-filesystem"],
                "args": ["/tmp"],
                "enabled": False,  # Disabled by default, user can enable
                "auto_start": False
            },
            {
                "name": "git",
                "display_name": "Git Tools", 
                "description": "Git repository management tools",
                "transport": "stdio",
                "command": ["npx", "-y", "@modelcontextprotocol/server-git"],
                "args": ["--repository", "."],
                "enabled": False,
                "auto_start": False
            },
            {
                "name": "github",
                "display_name": "GitHub Tools",
                "description": "GitHub API integration tools",
                "transport": "stdio", 
                "command": ["npx", "-y", "@modelcontextprotocol/server-github"],
                "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "your_github_token_here"},
                "enabled": False,
                "auto_start": False
            },
            {
                "name": "brave_search",
                "display_name": "Brave Search",
                "description": "Web search using Brave Search API",
                "transport": "stdio",
                "command": ["npx", "-y", "@modelcontextprotocol/server-brave-search"],
                "env": {"BRAVE_API_KEY": "your_brave_api_key_here"},
                "enabled": False,
                "auto_start": False
            }
        ]
        
        for server_data in default_servers:
            server = MCPServer.from_dict(server_data)
            self.servers[server.name] = server
        
        self.save_servers()
        logger.info(f"Created {len(default_servers)} default MCP server configurations")
    
    async def start_server(self, server_name: str) -> bool:
        """Start an MCP server."""
        if server_name not in self.servers:
            logger.error(f"Unknown MCP server: {server_name}")
            return False
        
        server = self.servers[server_name]
        if not server.enabled:
            logger.warning(f"MCP server {server_name} is disabled")
            return False
        
        if server_name in self.clients and self.clients[server_name].is_connected():
            logger.info(f"MCP server {server_name} is already running")
            return True
        
        try:
            client = MCPClient(server)
            success = await client.connect()
            
            if success:
                self.clients[server_name] = client
                logger.info(f"Started MCP server: {server_name}")
                return True
            else:
                logger.error(f"Failed to start MCP server: {server_name}")
                return False
                
        except Exception as e:
            logger.error(f"Error starting MCP server {server_name}: {e}")
            return False
    
    async def stop_server(self, server_name: str) -> bool:
        """Stop an MCP server."""
        if server_name not in self.clients:
            return True  # Already stopped
        
        try:
            client = self.clients[server_name]
            await client.disconnect()
            del self.clients[server_name]
            logger.info(f"Stopped MCP server: {server_name}")
            return True
        except Exception as e:
            logger.error(f"Error stopping MCP server {server_name}: {e}")
            return False
    
    async def start_all_servers(self):
        """Start all enabled servers with auto_start=True."""
        for server_name, server in self.servers.items():
            if server.enabled and server.auto_start:
                await self.start_server(server_name)
    
    async def stop_all_servers(self):
        """Stop all running servers."""
        for server_name in list(self.clients.keys()):
            await self.stop_server(server_name)
    
    def get_all_tools(self) -> List[MCPTool]:
        """Get all available tools from all servers."""
        tools = []
        
        # Add local tools
        tools.extend(self.local_tools_server.get_tools())
        
        # Add tools from connected servers
        for client in self.clients.values():
            if client.is_connected():
                tools.extend(client.get_tools())
        
        return tools
    
    def get_all_resources(self) -> List[MCPResource]:
        """Get all available resources from all servers."""
        resources = []
        
        # Add resources from connected servers
        for client in self.clients.values():
            if client.is_connected():
                resources.extend(client.get_resources())
        
        return resources
    
    def get_tool(self, tool_name: str) -> Optional[MCPTool]:
        """Get a specific tool by name."""
        # Check local tools first
        local_tool = self.local_tools_server.get_tool(tool_name)
        if local_tool:
            return local_tool
        
        # Check connected servers
        for client in self.clients.values():
            if client.is_connected():
                tool = client.get_tool(tool_name)
                if tool:
                    return tool
        
        return None
    
    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> MCPToolResult:
        """Call a tool by name."""
        # Check if it's a local tool
        local_tool = self.local_tools_server.get_tool(tool_name)
        if local_tool:
            return await self.local_tools_server.call_tool(tool_name, arguments)
        
        # Find the tool in connected servers
        for client in self.clients.values():
            if client.is_connected():
                tool = client.get_tool(tool_name)
                if tool:
                    return await client.call_tool(tool_name, arguments)
        
        return MCPToolResult(
            "",
            [{"type": "text", "text": f"Tool not found: {tool_name}"}],
            True
        )
    
    async def get_resource(self, uri: str) -> Optional[Any]:
        """Get a resource by URI."""
        # Find the resource in connected servers
        for client in self.clients.values():
            if client.is_connected():
                resource = client.get_resource(uri)
                if resource:
                    return await client.get_resource(uri)
        
        return None
    
    def add_server(self, server: MCPServer, probe: bool = True) -> bool:
        """Add a new MCP server configuration. If probe=True, attempt to auto-discover tools/metadata."""
        try:
            self.servers[server.name] = server
            self.save_servers()
            logger.info(f"Added MCP server: {server.name}")
            
            # Optionally probe HTTP endpoints to gather tools and metadata
            if probe and server.transport == MCPTransport.HTTP:
                try:
                    # schedule probe in background
                    asyncio.create_task(self._probe_and_update_server(server))
                except Exception as e:
                    logger.debug(f"Failed to schedule probe for {server.name}: {e}")
            
            return True
        except Exception as e:
            logger.error(f"Error adding MCP server {server.name}: {e}")
            return False
    
    async def _probe_and_update_server(self, server: MCPServer):
        """Background task to probe server and update config with discovered tools/metadata."""
        try:
            base_url = server.command[0] if server.command else server.display_name
            result = await probe_mcp_server_auto(base_url, api_key=(server.env or {}).get("API_KEY"))
            if result.get("healthy"):
                # Summarize tools and store in metadata
                tools_summary = summarize_tools(result.get("tools", []))
                server.metadata = server.metadata or {}
                server.metadata.update({"discovered_tools": tools_summary, "discovery_source": result.get("metadata", {})})
                self.save_servers()
                logger.info(f"Auto-discovery updated server {server.name} with {len(tools_summary)} tools")
        except Exception as e:
            logger.debug(f"Probe/update failed for server {server.name}: {e}")
    
    def remove_server(self, server_name: str) -> bool:
        """Remove an MCP server configuration."""
        if server_name not in self.servers:
            return False
        
        try:
            # Stop the server if it's running
            if server_name in self.clients:
                asyncio.create_task(self.stop_server(server_name))
            
            # Remove from configuration
            del self.servers[server_name]
            self.save_servers()
            logger.info(f"Removed MCP server: {server_name}")
            return True
        except Exception as e:
            logger.error(f"Error removing MCP server {server_name}: {e}")
            return False
    
    def update_server(self, server_name: str, **updates) -> bool:
        """Update an MCP server configuration."""
        if server_name not in self.servers:
            return False
        
        try:
            server = self.servers[server_name]
            
            # Update fields
            for key, value in updates.items():
                if hasattr(server, key):
                    setattr(server, key, value)
            
            self.save_servers()
            logger.info(f"Updated MCP server: {server_name}")
            return True
        except Exception as e:
            logger.error(f"Error updating MCP server {server_name}: {e}")
            return False
    
    def get_server_status(self) -> Dict[str, Dict[str, Any]]:
        """Get status of all servers."""
        status = {}
        
        # Local tools server (always available)
        status["local_tools"] = {
            "name": "Local Tools",
            "connected": True,
            "tools_count": len(self.local_tools_server.get_tools()),
            "resources_count": 0,
            "type": "local"
        }
        
        # External servers
        for server_name, server in self.servers.items():
            client = self.clients.get(server_name)
            is_connected = client.is_connected() if client else False
            
            status[server_name] = {
                "name": server.display_name,
                "connected": is_connected,
                "enabled": server.enabled,
                "auto_start": server.auto_start,
                "tools_count": len(client.get_tools()) if client and is_connected else 0,
                "resources_count": len(client.get_resources()) if client and is_connected else 0,
                "type": "external"
            }
        
        return status
    
    def get_tools_by_server(self) -> Dict[str, List[MCPTool]]:
        """Get tools grouped by server."""
        tools_by_server = {}
        
        # Local tools
        tools_by_server["local_tools"] = self.local_tools_server.get_tools()
        
        # External server tools
        for server_name, client in self.clients.items():
            if client.is_connected():
                tools_by_server[server_name] = client.get_tools()
        
        return tools_by_server
    
    async def health_check_all(self) -> Dict[str, bool]:
        """Perform health check on all servers."""
        health_status = {}
        
        # Local tools are always healthy
        health_status["local_tools"] = True
        
        # Check external servers
        for server_name in self.servers.keys():
            try:
                if server_name in self.clients:
                    health_status[server_name] = self.clients[server_name].is_connected()
                else:
                    health_status[server_name] = False
            except Exception as e:
                logger.error(f"Health check failed for {server_name}: {e}")
                health_status[server_name] = False
        
        return health_status
    
    async def cleanup(self):
        """Clean up all connections and resources."""
        await self.stop_all_servers()
        logger.info("MCP Server Manager cleanup complete")
