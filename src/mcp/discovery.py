"""
MCP discovery utilities

Provides helpers to probe MCP servers (http) for available tools and metadata,
so the server manager can auto-configure or validate remote servers.

This module is safe and non-destructive: probes use read-only endpoints when
available (e.g., /health, /tools, /models) and do not change remote state.
"""
import asyncio
import json
import logging
from typing import Dict, Any, List, Optional
import aiohttp

logger = logging.getLogger(__name__)


async def probe_http_mcp(base_url: str, timeout: float = 5.0) -> Dict[str, Any]:
    """
    Probe an HTTP MCP server for basic info and tools.

    Tries the following endpoints (if present):
      - GET {base_url}/health
      - GET {base_url}/tools
      - GET {base_url}/.well-known/mcp or {base_url}/metadata

    Returns a dict with keys: healthy(bool), tools(list), metadata(dict)
    """
    result: Dict[str, Any] = {"healthy": False, "tools": [], "metadata": {}}
    if not base_url:
        return result

    # Normalize base_url
    base_url = base_url.rstrip("/")

    timeout_cfg = aiohttp.ClientTimeout(total=timeout)
    headers = {"User-Agent": "Augie-MCP-Discovery/1.0"}

    async with aiohttp.ClientSession(timeout=timeout_cfg, headers=headers) as session:
        # health
        health_endpoints = [f"{base_url}/health", f"{base_url}/status"]
        for url in health_endpoints:
            try:
                async with session.get(url) as resp:
                    if resp.status == 200:
                        result["healthy"] = True
                        try:
                            data = await resp.json()
                            result["metadata"].setdefault("health", data)
                        except Exception:
                            result["metadata"].setdefault("health", {"status": "ok"})
                        break
            except Exception:
                continue

        # tools endpoint
        tools_endpoints = [f"{base_url}/tools", f"{base_url}/mcp/tools", f"{base_url}/.well-known/mcp/tools"]
        for url in tools_endpoints:
            try:
                async with session.get(url) as resp:
                    if resp.status == 200:
                        try:
                            data = await resp.json()
                            # Expecting a list of tools or a dict with 'tools'
                            if isinstance(data, dict) and "tools" in data:
                                tools = data["tools"]
                            elif isinstance(data, list):
                                tools = data
                            else:
                                tools = []
                            result["tools"] = tools
                            result["metadata"].setdefault("tools_source", url)
                            break
                        except Exception:
                            continue
            except Exception:
                continue

        # generic metadata
        meta_endpoints = [f"{base_url}/.well-known/mcp", f"{base_url}/metadata", f"{base_url}/info"]
        for url in meta_endpoints:
            try:
                async with session.get(url) as resp:
                    if resp.status == 200:
                        try:
                            data = await resp.json()
                            result["metadata"].update(data if isinstance(data, dict) else {"info": data})
                            break
                        except Exception:
                            continue
            except Exception:
                continue

    return result


async def probe_mcp_server_auto(base_url: str, api_key: Optional[str] = None, timeout: float = 5.0) -> Dict[str, Any]:
    """
    High-level probe that sets headers optionally and returns discovery result.

    This function exists to centralize any auth header injection in the future.
    """
    headers = {}
    if api_key:
        headers["Authorization"] = f"Bearer {api_key}"

    # Currently only supports HTTP-style probes; add other transports later.
    try:
        res = await probe_http_mcp(base_url=base_url, timeout=timeout)
        return res
    except Exception as e:
        logger.debug(f"probe_mcp_server_auto error for {base_url}: {e}")
        return {"healthy": False, "tools": [], "metadata": {}}


def summarize_tools(tools: List[Any]) -> List[Dict[str, Any]]:
    """
    Normalize tool list into a lightweight summary for storing in server config.

    Tools can be dicts or objects with 'name' and 'description'.
    """
    summary = []
    for t in tools:
        try:
            if isinstance(t, dict):
                name = t.get("name") or t.get("id") or t.get("tool")
                desc = t.get("description") or t.get("summary") or ""
                params = t.get("parameters") or t.get("args") or []
            else:
                name = getattr(t, "name", None)
                desc = getattr(t, "description", "")
                params = getattr(t, "parameters", [])
            summary.append({"name": name, "description": desc, "parameter_count": len(params)})
        except Exception:
            continue
    return summary
