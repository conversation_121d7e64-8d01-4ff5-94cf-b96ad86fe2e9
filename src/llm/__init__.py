"""
LLM integration module for the Cherry Studio Clone.

This package contains implementations for various LLM providers and a base interface for adding new providers.
Supports all major AI providers including OpenAI, Anthropic, Google, local models, and more.
"""

from .base import BaseLLM, LLMResponse
from .openai import OpenAIClient
from .anthropic import Anthropic<PERSON>lient
from .gemini import GeminiClient
from .ollama import OllamaClient
from .lmstudio import LMStudioClient
from .vllm import VLL<PERSON>lient
from .cohere import CohereClient
from .mistral import MistralClient
from .perplexity import PerplexityClient
from .groq import GroqClient
from .provider_manager import ProviderManager, ProviderInfo

__all__ = [
    'BaseLLM',
    'LLMResponse',
    'OpenAIClient',
    'AnthropicClient',
    'GeminiClient',
    'OllamaClient',
    'LMStudioClient',
    'VLLMClient',
    'CohereClient',
    'MistralClient',
    'PerplexityClient',
    'GroqClient',
    'ProviderManager',
    'ProviderInfo'
]
