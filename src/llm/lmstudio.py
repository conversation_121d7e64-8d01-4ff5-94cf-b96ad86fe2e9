"""
LM Studio client for the VoiceFlow AI assistant.

This module provides integration with LM Studio's local LLM server.
"""
import json
import logging
from typing import Any, Dict, List, Optional, AsyncGenerator
import aiohttp

from .base import BaseLLM, LLMResponse

logger = logging.getLogger(__name__)

class LMStudioClient(BaseLLM):
    """Client for interacting with LM Studio's local LLM server."""
    
    def _initialize_client(self) -> str:
        """Initialize the LM Studio client configuration."""
        self.base_url = self._get_config('base_url', 'http://localhost:1234')
        self.model = self._get_config('model', 'local-model')
        return self.base_url
    
    async def _make_request(self, endpoint: str, method: str = 'POST', 
                          json_data: Optional[dict] = None) -> dict:
        """Make an HTTP request to the LM Studio API."""
        url = f"{self.base_url.rstrip('/')}/{endpoint.lstrip('/')}"
        
        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
        
        # Log the request details
        logger.info(f"Sending {method} request to {url}")
        if json_data:
            logger.info(f"Request payload: {json.dumps(json_data, indent=2, ensure_ascii=False)}")
        
        # Add connection debugging
        conn = aiohttp.TCPConnector(force_close=True, enable_cleanup_closed=True)
        timeout = aiohttp.ClientTimeout(total=300)
        
        try:
            async with aiohttp.ClientSession(connector=conn) as session:
                async with session.request(
                    method=method,
                    url=url,
                    json=json_data,
                    headers=headers,
                    timeout=timeout
                ) as response:
                    response_text = await response.text()
                    logger.info(f"Response status: {response.status}")
                    logger.info(f"Response headers: {dict(response.headers)}")
                    
                    try:
                        response_data = await response.json()
                        logger.info(f"Response JSON: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
                        return response_data
                    except json.JSONDecodeError:
                        logger.error(f"Failed to decode JSON response: {response_text}")
                        return {"error": f"Invalid JSON response: {response_text}"}
                    
        except aiohttp.ClientError as e:
            logger.error(f"HTTP request failed: {str(e)}")
            return {"error": f"HTTP request failed: {str(e)}"}
        except Exception as e:
            logger.error(f"Unexpected error: {str(e)}", exc_info=True)
            return {"error": f"Unexpected error: {str(e)}"}
        finally:
            if 'conn' in locals():
                await conn.close()
                    
    def _format_messages(self, messages: List[Dict[str, str]]) -> List[Dict[str, str]]:
        """Format messages for the LM Studio API."""
        # LM Studio uses the same format as OpenAI's chat completions API
        return [{"role": msg.get("role", "user"), "content": msg.get("content", "")} 
                for msg in messages]
    
    async def generate_response(
        self, 
        messages: List[Dict[str, str]],
        **kwargs
    ) -> LLMResponse:
        """Generate a response using the LM Studio API."""
        model = kwargs.get('model', self.model)
        max_tokens = kwargs.get('max_tokens', self.max_tokens)
        temperature = kwargs.get('temperature', self.temperature)
        
        # Format messages for the API
        formatted_messages = self._format_messages(messages)
        
        # Prepare the request payload
        payload = {
            "model": model,
            "messages": formatted_messages,
            "max_tokens": max_tokens,
            "temperature": temperature,
            "stream": False
        }
        
        # Add any additional parameters
        if 'top_p' in kwargs:
            payload['top_p'] = kwargs['top_p']
        if 'frequency_penalty' in kwargs:
            payload['frequency_penalty'] = kwargs['frequency_penalty']
        if 'presence_penalty' in kwargs:
            payload['presence_penalty'] = kwargs['presence_penalty']
        
        try:
            # Make the API request
            response = await self._make_request(
                '/v1/chat/completions',
                json_data=payload
            )
            
            # Extract the response content
            if 'choices' in response and len(response['choices']) > 0:
                content = response['choices'][0]['message']['content']
                finish_reason = response['choices'][0].get('finish_reason', 'stop')
            else:
                content = ""
                finish_reason = "no_response"
            
            # Extract token usage if available
            prompt_tokens = response.get('usage', {}).get('prompt_tokens', 0)
            completion_tokens = response.get('usage', {}).get('completion_tokens', 0)
            
            return LLMResponse(
                content=content,
                model=model,
                prompt_tokens=prompt_tokens,
                completion_tokens=completion_tokens,
                total_tokens=prompt_tokens + completion_tokens,
                finish_reason=finish_reason,
                metadata={
                    'id': response.get('id'),
                    'created': response.get('created'),
                    'object': response.get('object'),
                    'model': response.get('model')
                }
            )
            
        except Exception as e:
            logger.error(f"Error generating response from LM Studio: {e}")
            raise
    
    async def stream_response(
        self, 
        messages: List[Dict[str, str]],
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """Stream the response from the LM Studio API."""
        model = kwargs.get('model', self.model)
        max_tokens = kwargs.get('max_tokens', self.max_tokens)
        temperature = kwargs.get('temperature', self.temperature)
        
        # Format messages for the API
        formatted_messages = self._format_messages(messages)
        
        # Prepare the request payload
        payload = {
            "model": model,
            "messages": formatted_messages,
            "max_tokens": max_tokens,
            "temperature": temperature,
            "stream": True
        }
        
        # Add any additional parameters
        if 'top_p' in kwargs:
            payload['top_p'] = kwargs['top_p']
        if 'frequency_penalty' in kwargs:
            payload['frequency_penalty'] = kwargs['frequency_penalty']
        if 'presence_penalty' in kwargs:
            payload['presence_penalty'] = kwargs['presence_penalty']
        
        url = f"{self.base_url.rstrip('/')}/v1/chat/completions"
        headers = {
            'Content-Type': 'application/json',
            'Accept': 'text/event-stream'
        }
        
        async with aiohttp.ClientSession() as session:
            try:
                async with session.post(
                    url,
                    json=payload,
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=300)
                ) as response:
                    response.raise_for_status()
                    
                    async for line in response.content:
                        if line.startswith(b'data: '):
                            chunk = line[6:].strip()
                            if chunk == b'[DONE]':
                                break
                                
                            try:
                                data = json.loads(chunk)
                                if 'choices' in data and len(data['choices']) > 0:
                                    delta = data['choices'][0].get('delta', {})
                                    if 'content' in delta:
                                        yield delta['content']
                            except json.JSONDecodeError:
                                continue
                                
            except Exception as e:
                logger.error(f"Error during streaming response: {e}")
                raise
    
    async def count_tokens(self, text: str, model: Optional[str] = None) -> int:
        """Estimate the number of tokens in the given text."""
        # This is a rough estimate based on English text (4 chars ~= 1 token)
        return (len(text) + 3) // 4
