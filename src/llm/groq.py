"""
Groq client for ultra-fast LLM inference.
"""
import asyncio
import aiohttp
import json
import logging
from typing import List, Dict, Any, Optional, AsyncGenerator, Union

from .base import BaseLLM, LLMResponse

logger = logging.getLogger(__name__)

class GroqClient(BaseLLM):
    """Client for Groq's ultra-fast LLM inference."""
    
    def _initialize_client(self) -> aiohttp.ClientSession:
        """Initialize the Groq client."""
        # Get Groq-specific configuration
        self.api_key = self._get_config('api_key', '')
        self.base_url = self._get_config('base_url', 'https://api.groq.com/openai/v1')
        
        if not self.api_key:
            raise ValueError("Groq API key is required")
        
        # Create headers
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json',
            'User-Agent': 'Cherry-Studio-Clone/1.0'
        }
        
        # Create session with timeout
        timeout = aiohttp.ClientTimeout(total=self.timeout)
        return aiohttp.ClientSession(
            headers=headers,
            timeout=timeout,
            connector=aiohttp.TCPConnector(limit=10)
        )
    
    async def generate_response(
        self,
        messages: List[Dict[str, str]],
        **kwargs
    ) -> Union[str, AsyncGenerator[str, None]]:
        """Generate a response using Groq."""
        try:
            # Prepare the request payload
            payload = {
                'model': self.model or 'llama-3.1-70b-versatile',
                'messages': self._format_messages(messages),
                'max_tokens': kwargs.get('max_tokens', self.max_tokens),
                'temperature': kwargs.get('temperature', self.temperature),
                'top_p': kwargs.get('top_p', self.top_p),
                'stream': kwargs.get('stream', self.stream),
                'stop': kwargs.get('stop', None),
            }
            
            # Remove None values
            payload = {k: v for k, v in payload.items() if v is not None}
            
            logger.debug(f"Groq request payload: {json.dumps(payload, indent=2)}")
            
            if payload.get('stream', False):
                return self._stream_response(payload)
            else:
                return await self._single_response(payload)
                
        except Exception as e:
            logger.error(f"Error in Groq generate_response: {e}")
            raise
    
    async def _single_response(self, payload: Dict[str, Any]) -> str:
        """Handle non-streaming response."""
        url = f"{self.base_url}/chat/completions"
        
        async with self.client.post(url, json=payload) as response:
            if response.status != 200:
                error_text = await response.text()
                logger.error(f"Groq API error {response.status}: {error_text}")
                raise Exception(f"Groq API error {response.status}: {error_text}")
            
            data = await response.json()
            
            # Extract the response content
            if 'choices' in data and len(data['choices']) > 0:
                choice = data['choices'][0]
                content = choice['message']['content']
                logger.info(f"Groq response received: {len(content)} characters")
                return content
            else:
                logger.error(f"Unexpected Groq response format: {data}")
                raise Exception("Invalid response format from Groq")
    
    async def _stream_response(self, payload: Dict[str, Any]) -> AsyncGenerator[str, None]:
        """Handle streaming response."""
        url = f"{self.base_url}/chat/completions"
        
        async with self.client.post(url, json=payload) as response:
            if response.status != 200:
                error_text = await response.text()
                logger.error(f"Groq API error {response.status}: {error_text}")
                raise Exception(f"Groq API error {response.status}: {error_text}")
            
            async for line in response.content:
                line = line.decode('utf-8').strip()
                
                if line.startswith('data: '):
                    data_str = line[6:]  # Remove 'data: ' prefix
                    
                    if data_str == '[DONE]':
                        break
                    
                    try:
                        data = json.loads(data_str)
                        
                        if 'choices' in data and len(data['choices']) > 0:
                            choice = data['choices'][0]
                            
                            if 'delta' in choice and 'content' in choice['delta']:
                                content = choice['delta']['content']
                                if content:
                                    yield content
                    
                    except json.JSONDecodeError:
                        continue  # Skip malformed JSON
    
    async def count_tokens(self, text: str) -> int:
        """Estimate token count for Groq."""
        # Simple estimation: ~4 characters per token
        return len(text) // 4
    
    def _format_messages(self, messages: List[Dict[str, str]]) -> List[Dict[str, str]]:
        """Format messages for Groq (OpenAI-compatible format)."""
        formatted = []
        
        for message in messages:
            role = message.get('role', 'user')
            content = message.get('content', '')
            
            # Groq supports OpenAI-compatible format
            formatted.append({
                'role': role,
                'content': content
            })
        
        return formatted
    
    async def get_available_models(self) -> List[str]:
        """Get list of available Groq models."""
        try:
            url = f"{self.base_url}/models"
            
            async with self.client.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    if 'data' in data:
                        return [model['id'] for model in data['data']]
                    else:
                        return self._get_default_models()
                else:
                    logger.warning(f"Could not fetch Groq models: {response.status}")
                    return self._get_default_models()
        except Exception as e:
            logger.warning(f"Error fetching Groq models: {e}")
            return self._get_default_models()
    
    def _get_default_models(self) -> List[str]:
        """Get default Groq models."""
        return [
            'llama-3.1-70b-versatile',
            'llama-3.1-8b-instant',
            'llama3-70b-8192',
            'llama3-8b-8192',
            'mixtral-8x7b-32768',
            'gemma-7b-it',
            'gemma2-9b-it'
        ]
    
    async def health_check(self) -> bool:
        """Check if Groq API is accessible."""
        try:
            url = f"{self.base_url}/models"
            async with self.client.get(url) as response:
                return response.status == 200
        except Exception as e:
            logger.error(f"Groq health check failed: {e}")
            return False
    
    async def __aenter__(self):
        """Async context manager entry."""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if hasattr(self, 'client') and self.client:
            await self.client.close()
