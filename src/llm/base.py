"""
Base classes and interfaces for LLM providers.
"""
from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import List, Dict, Any, Optional, AsyncGenerator, Union
import json
import logging

logger = logging.getLogger(__name__)

@dataclass
class LLMResponse:
    """Structured response from an LLM."""
    content: str
    model: str
    prompt_tokens: int = 0
    completion_tokens: int = 0
    total_tokens: int = 0
    finish_reason: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

class BaseLLM(ABC):
    """Abstract base class for LLM providers."""
    
    def __init__(self, config):
        """Initialize the LLM client with configuration.
        
        Args:
            config: Configuration object (ConfigParser) for the LLM provider
        """
        self.config = config
        self.model = self._get_config('model')
        self.max_tokens = self._get_config('max_tokens', 1000, int)
        self.temperature = self._get_config('temperature', 0.7, float)
        self.top_p = self._get_config('top_p', 1.0, float)
        self.stream = self._get_config('stream', False, bool)
        self.timeout = self._get_config('timeout', 30.0, float)
        
        # Initialize the client
        self.client = self._initialize_client()
    
    def _get_config(self, key: str, default: Any = None, type_func=None):
        """Helper to get a configuration value with a default.
        
        Args:
            key: Configuration key to retrieve
            default: Default value if key is not found
            type_func: Optional function to convert the value (e.g., int, float, bool)
            
        Returns:
            The configuration value, optionally converted to the specified type
        """
        # Get the provider name from the class name (e.g., 'OpenAIClient' -> 'OPENAI')
        provider = self.__class__.__name__.replace('Client', '').upper()
        
        # Check in this order:
        # 1. Provider-specific section (e.g., [LMSTUDIO])
        # 2. LLM section
        # 3. DEFAULT section
        if self.config.has_section(provider) and key in self.config[provider]:
            value = self.config[provider][key]
        elif self.config.has_section('LLM') and key in self.config['LLM']:
            value = self.config['LLM'][key]
        else:
            value = self.config.get('DEFAULT', key, fallback=default)
        
        # Convert the value if a type function is provided
        if value is not None and type_func is not None and value != default:
            try:
                if type_func == bool:
                    # Handle boolean values (case-insensitive)
                    value = str(value).lower() in ('true', 'yes', '1', 'on')
                else:
                    value = type_func(value)
            except (ValueError, TypeError):
                logger.warning(f"Could not convert config value '{key}={value}' to {type_func.__name__}, using default: {default}")
                value = default
        
        return value
    
    @abstractmethod
    def _initialize_client(self) -> Any:
        """Initialize and return the LLM client.
        
        Returns:
            Initialized LLM client
        """
        pass
    
    @abstractmethod
    async def generate_response(
        self,
        messages: List[Dict[str, str]],
        **kwargs
    ) -> Union[str, AsyncGenerator[str, None]]:
        """Generate a response from the LLM.
        
        Args:
            messages: List of message dictionaries with 'role' and 'content'
            **kwargs: Additional parameters for the generation
            
        Returns:
            Generated text or an async generator for streaming responses
        """
        pass
    
    @abstractmethod
    async def count_tokens(self, text: str) -> int:
        """Count the number of tokens in the given text.
        
        Args:
            text: Input text to count tokens for
            
        Returns:
            Number of tokens
        """
        pass
    
    def _format_messages(self, messages: List[Dict[str, str]]) -> Any:
        """Format messages for the specific LLM API.
        
        Args:
            messages: List of message dictionaries with 'role' and 'content'
            
        Returns:
            Formatted messages for the LLM API
        """
        # Default implementation returns messages as-is
        return messages
    
    def _create_response(
        self,
        content: str,
        model: str,
        prompt_tokens: int = 0,
        completion_tokens: int = 0,
        finish_reason: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> LLMResponse:
        """Create a standardized LLMResponse object."""
        return LLMResponse(
            content=content,
            model=model,
            prompt_tokens=prompt_tokens,
            completion_tokens=completion_tokens,
            total_tokens=prompt_tokens + completion_tokens,
            finish_reason=finish_reason,
            metadata=metadata or {}
        )
    
    async def __call__(self, prompt: str, **kwargs) -> str:
        """Convenience method for simple text completion."""
        messages = [{"role": "user", "content": prompt}]
        response = await self.generate_response(messages, **kwargs)
        return response.content if isinstance(response, LLMResponse) else response
