"""
Google Gemini API client implementation.
"""
import logging
from typing import List, Dict, Any, Optional, AsyncGenerator, Union, cast

import google.generativeai as genai
from google.generativeai.types import GenerateContentResponse, ContentType

from .base import BaseLLM, LLMResponse

logger = logging.getLogger(__name__)

class GeminiClient(BaseLLM):
    """Client for interacting with Google's Gemini models."""
    
    def _initialize_client(self) -> None:
        """Initialize the Gemini client."""
        api_key = self._get_config('api_key')
        if not api_key or api_key == 'your_gemini_api_key':
            raise ValueError("Gemini API key not found in configuration")
            
        genai.configure(api_key=api_key)
        self.client = genai
    
    def _format_messages(self, messages: List[Dict[str, str]]) -> List[ContentType]:
        """Format messages for the Gemini API."""
        formatted = []
        
        for msg in messages:
            role = msg['role']
            content = msg['content']
            
            # Gemini uses 'user' and 'model' roles
            if role == 'assistant':
                role = 'model'
            elif role == 'system':
                # For system messages, we'll prepend to the first user message
                if formatted and formatted[-1]['role'] == 'user':
                    formatted[-1]['parts'].append(f"[System: {content}]")
                continue
            
            # Add the message
            formatted.append({
                'role': role,
                'parts': [content]
            })
        
        return formatted
    
    async def generate_response(
        self,
        messages: List[Dict[str, str]],
        **kwargs
    ) -> Union[str, AsyncGenerator[str, None]]:
        """Generate a response from Gemini."""
        # Get parameters with overrides from kwargs
        model_name = kwargs.get('model', self.model)
        temperature = kwargs.get('temperature', self.temperature)
        max_tokens = kwargs.get('max_tokens', self.max_tokens)
        stream = kwargs.get('stream', self.stream)
        
        # Format messages for the API
        formatted_messages = self._format_messages(messages)
        
        # Get the model
        model = self.client.GenerativeModel(model_name)
        
        # Configure generation parameters
        generation_config = {
            'temperature': temperature,
            'max_output_tokens': max_tokens,
        }
        
        try:
            if stream:
                return self._stream_response(model, formatted_messages, generation_config)
            
            # Non-streaming response
            response: GenerateContentResponse = await model.generate_content_async(
                contents=formatted_messages,
                generation_config=generation_config,
            )
            
            # Extract the response content
            content = ""
            if response.text:
                content = response.text
            
            # Get token counts if available
            prompt_tokens = 0
            completion_tokens = 0
            if hasattr(response, 'usage_metadata'):
                prompt_tokens = getattr(response.usage_metadata, 'prompt_token_count', 0)
                completion_tokens = getattr(response.usage_metadata, 'candidates_token_count', 0)
            
            # Create a standardized response
            return self._create_response(
                content=content,
                model=model_name,
                prompt_tokens=prompt_tokens,
                completion_tokens=completion_tokens,
                finish_reason=response.prompt_feedback.block_reason if hasattr(response, 'prompt_feedback') else None,
                metadata={
                    "safety_ratings": [
                        {
                            'category': cat.name,
                            'probability': getattr(rating, 'probability', None).name,
                            'blocked': getattr(rating, 'blocked', None)
                        }
                        for cat, rating in response.prompt_feedback.safety_ratings
                    ] if hasattr(response, 'prompt_feedback') and hasattr(response.prompt_feedback, 'safety_ratings') else []
                }
            )
            
        except Exception as e:
            logger.error(f"Error generating response from Gemini: {e}")
            raise
    
    async def _stream_response(
        self,
        model: Any,
        messages: List[Dict[str, Any]],
        generation_config: Dict[str, Any]
    ) -> AsyncGenerator[str, None]:
        """Handle streaming responses from Gemini."""
        try:
            response = await model.generate_content_async(
                contents=messages,
                generation_config=generation_config,
                stream=True
            )
            
            async for chunk in response:
                if hasattr(chunk, 'text'):
                    yield chunk.text
                    
        except Exception as e:
            logger.error(f"Error during streaming response: {e}")
            raise
    
    async def count_tokens(self, text: str, model: Optional[str] = None) -> int:
        """Count the number of tokens in the given text using Gemini's tokenizer."""
        try:
            model_name = model or self.model or "gemini-pro"
            model = self.client.GenerativeModel(model_name)
            response = await model.count_tokens_async(text)
            return response.total_tokens
        except Exception as e:
            logger.error(f"Error counting tokens: {e}")
            # Fallback to a simple approximation if the API call fails
            return len(text) // 4  # Rough approximation
    
    async def get_embeddings(self, text: str, model: str = "models/embedding-001") -> List[float]:
        """Get embeddings using the Gemini Embedding model."""
        try:
            # Use the embedding model
            result = await self.client.embed_content_async(
                model=model,
                content=text,
                task_type="retrieval_document"
            )
            
            if hasattr(result, 'embedding'):
                return result.embedding.values
            elif hasattr(result, 'values'):
                return result.values
            else:
                raise ValueError("Unexpected response format from embeddings API")
                
        except Exception as e:
            logger.error(f"Error getting embeddings: {e}")
            raise
