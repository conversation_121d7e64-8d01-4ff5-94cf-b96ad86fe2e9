"""
Ollama API client implementation for local LLM models.
"""
import logging
import json
from typing import List, Dict, Any, Optional, AsyncGenerator, Union

import aiohttp
from pydantic import BaseModel, Field

from .base import BaseLLM, LLMResponse

logger = logging.getLogger(__name__)

class OllamaGenerateRequest(BaseModel):
    """Request model for Ollama's generate endpoint."""
    model: str
    prompt: str
    system: Optional[str] = None
    template: Optional[str] = None
    context: Optional[List[int]] = None
    stream: bool = False
    raw: bool = False
    format: Optional[str] = None
    options: Dict[str, Any] = Field(default_factory=dict)

class OllamaClient(BaseLLM):
    """Client for interacting with local Ollama models."""
    
    def _initialize_client(self) -> str:
        """Initialize the Ollama client configuration."""
        self.base_url = self._get_config('base_url', 'http://localhost:11434')
        self.model = self._get_config('model', 'llama2')
        return self.base_url
    
    async def _make_request(self, endpoint: str, method: str = 'GET', json_data: Optional[dict] = None) -> dict:
        """Make an HTTP request to the Ollama API."""
        url = f"{self.base_url.rstrip('/')}/{endpoint.lstrip('/')}"
        
        async with aiohttp.ClientSession() as session:
            async with session.request(method, url, json=json_data) as response:
                response.raise_for_status()
                return await response.json()
    
    async def _stream_request(self, endpoint: str, json_data: dict) -> AsyncGenerator[dict, None]:
        """Make a streaming request to the Ollama API."""
        url = f"{self.base_url.rstrip('/')}/{endpoint.lstrip('/')}"
        request_id = id(self)
        logger.debug(f"[Req {request_id}] Starting streaming request to {url}")
        logger.debug(f"[Req {request_id}] Request data: {json.dumps({k: v if k != 'prompt' else f'[{len(v)} chars]'} for k, v in json_data.items())}")
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=json_data) as response:
                    logger.debug(f"[Req {request_id}] Response status: {response.status}")
                    response.raise_for_status()
                    
                    line_count = 0
                    async for line in response.content:
                        line_count += 1
                        if line:
                            try:
                                data = json.loads(line.decode('utf-8'))
                                logger.debug(f"[Req {request_id}] Received line {line_count}, data: {json.dumps(data) if len(str(data)) < 100 else str(data)[:100] + '...'}")
                                yield data
                            except json.JSONDecodeError as e:
                                logger.warning(f"[Req {request_id}] Failed to decode JSON (line {line_count}): {line}", exc_info=True)
                                raise
                            except Exception as e:
                                logger.error(f"[Req {request_id}] Error processing line {line_count}: {e}", exc_info=True)
                                raise
                    
                    logger.debug(f"[Req {request_id}] Stream completed, received {line_count} lines")
                    
        except aiohttp.ClientError as e:
            logger.error(f"[Req {request_id}] HTTP client error during streaming request: {e}", exc_info=True)
            raise
        except Exception as e:
            logger.error(f"[Req {request_id}] Unexpected error during streaming request: {e}", exc_info=True)
            raise
    
    async def list_models(self) -> List[dict]:
        """List available Ollama models."""
        try:
            response = await self._make_request('api/tags')
            return response.get('models', [])
        except Exception as e:
            logger.error(f"Error listing models: {e}")
            return []
    
    async def pull_model(self, model_name: str) -> bool:
        """Pull a model from the Ollama library."""
        try:
            response = await self._make_request('api/pull', 'POST', {'name': model_name})
            logger.info(f"Model pull response: {response}")
            return True
        except Exception as e:
            logger.error(f"Error pulling model: {e}")
            return False
    
    def _format_messages(self, messages: List[Dict[str, str]]) -> str:
        """Format messages for the Ollama API."""
        formatted = []
        system_prompt = ""
        
        for msg in messages:
            role = msg['role']
            content = msg['content']
            
            if role == 'system':
                system_prompt += content + "\n"
            elif role == 'assistant':
                formatted.append(f"Assistant: {content}")
            else:  # user
                formatted.append(f"User: {content}")
        
        # Add system prompt at the beginning if present
        if system_prompt:
            formatted.insert(0, f"System: {system_prompt}")
        
        return "\n".join(formatted) + "\n\nAssistant:"
    
    async def generate_response(
        self,
        messages: List[Dict[str, str]],
        **kwargs
    ) -> Union[str, AsyncGenerator[str, None]]:
        """Generate a response using an Ollama model."""
        request_id = id(self)  # Unique ID for this request
        logger.info(f"[Req {request_id}] Starting generate_response with {len(messages)} messages")
        
        try:
            # Get parameters with overrides from kwargs
            model_name = kwargs.get('model', self.model)
            temperature = kwargs.get('temperature', self.temperature)
            max_tokens = kwargs.get('max_tokens', self.max_tokens)
            stream = kwargs.get('stream', self.stream)
            
            logger.info(f"[Req {request_id}] Generating response with model: {model_name}, stream: {stream}, max_tokens: {max_tokens}")
            
            # Format messages for the API
            prompt = self._format_messages(messages)
            logger.debug(f"[Req {request_id}] Formatted prompt (first 200 chars): {prompt[:200]}...")
            
            # Prepare the request
            request_data = {
                'model': model_name,
                'prompt': prompt,
                'stream': stream,
                'options': {
                    'temperature': temperature,
                    'num_predict': max_tokens,
                }
            }
            
            logger.debug(f"[Req {request_id}] Request data prepared")
            
            if stream:
                logger.info(f"[Req {request_id}] Using streaming response")
                return self._stream_response(request_data, request_id)
            
            # Non-streaming response
            logger.info(f"[Req {request_id}] Using non-streaming response")
            try:
                response = await self._make_request('api/generate', 'POST', request_data)
                logger.info(f"[Req {request_id}] Received non-streaming response")
                
                # Extract the response content
                content = response.get('response', '')
                logger.info(f"[Req {request_id}] Response content length: {len(content)} characters")
                
                # Log token counts if available
                prompt_tokens = response.get('prompt_eval_count', 0)
                completion_tokens = response.get('eval_count', 0)
                logger.debug(f"[Req {request_id}] Prompt tokens: {prompt_tokens}, Completion tokens: {completion_tokens}")
                
                # Create a standardized response
                result = self._create_response(
                    content=content,
                    model=model_name,
                    prompt_tokens=prompt_tokens,
                    completion_tokens=completion_tokens,
                    metadata={
                        'done': response.get('done', True),
                        'total_duration': response.get('total_duration', 0),
                        'load_duration': response.get('load_duration', 0),
                    }
                )
                
                logger.debug(f"[Req {request_id}] Created response object")
                return result
                
            except Exception as e:
                logger.error(f"[Req {request_id}] Error in non-streaming request: {e}", exc_info=True)
                raise
            
        except Exception as e:
            error_msg = f"[Req {request_id}] Error generating response from Ollama: {e}"
            logger.error(error_msg, exc_info=True)
            raise Exception(error_msg) from e
    
    async def _stream_response(self, request_data: dict, request_id: int) -> AsyncGenerator[str, None]:
        """Handle streaming responses from Ollama."""
        logger.info(f"[Req {request_id}] Starting stream response handler")
        full_response = ""
        chunk_count = 0
        last_log_size = 0
        
        try:
            logger.debug(f"[Req {request_id}] Starting to stream response from {self.base_url}/api/generate")
            
            async for chunk in self._stream_request('api/generate', request_data):
                chunk_count += 1
                logger.debug(f"[Req {request_id}] Received chunk #{chunk_count}, type: {type(chunk).__name__}, keys: {chunk.keys() if hasattr(chunk, 'keys') else 'N/A'}")
                
                if 'response' in chunk:
                    content = chunk['response']
                    # Only yield the new part of the response
                    new_content = content[len(full_response):]
                    
                    if new_content:  # Only log and yield if there's new content
                        logger.debug(f"[Req {request_id}] New content in chunk (length: {len(new_content)}): {new_content!r}")
                        full_response = content
                        
                        # Periodically log the full response size
                        if len(full_response) - last_log_size > 100:  # Log every ~100 chars
                            logger.info(f"[Req {request_id}] Response so far: {len(full_response)} chars")
                            last_log_size = len(full_response)
                            
                        yield new_content
                else:
                    logger.debug(f"[Req {request_id}] Chunk does not contain 'response' key: {chunk}")
                    
                    # Log progress every 10 chunks
                    if chunk_count % 10 == 0:
                        logger.info(f"[Req {request_id}] Received {chunk_count} chunks, total response length: {len(full_response)}")
                
                # Log any errors from the API
                if 'error' in chunk:
                    logger.error(f"[Req {request_id}] Error in stream response: {chunk['error']}")
            
            logger.info(f"[Req {request_id}] Stream completed after {chunk_count} chunks, total response length: {len(full_response)}")
                    
        except asyncio.CancelledError:
            logger.info(f"[Req {request_id}] Stream was cancelled")
            raise
            
        except Exception as e:
            logger.error(f"[Req {request_id}] Error during streaming response after {chunk_count} chunks: {e}", exc_info=True)
            raise
    
    async def count_tokens(self, text: str, model: Optional[str] = None) -> int:
        """Count the number of tokens in the given text."""
        # Ollama doesn't have a direct token counting endpoint,
        # so we'll use a simple approximation
        return len(text) // 4  # Rough approximation of tokens
    
    async def get_embeddings(self, text: str, model: Optional[str] = None) -> List[float]:
        """Get embeddings using an Ollama model."""
        model_name = model or self.model
        
        try:
            request_data = {
                'model': model_name,
                'prompt': text
            }
            
            response = await self._make_request('api/embeddings', 'POST', request_data)
            return response.get('embedding', [])
            
        except Exception as e:
            logger.error(f"Error getting embeddings: {e}")
            raise
