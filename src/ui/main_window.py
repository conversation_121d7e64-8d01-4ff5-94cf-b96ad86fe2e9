"""
Main application window for the VoiceFlow AI Assistant.
"""
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog
import asyncio
import logging
from pathlib import Path
from typing import Optional, Dict, Any, Callable, Coroutine, Any
from .modern_chat_window import ModernChat<PERSON>indow
from .provider_status_window import ProviderStatusWindow
from .mcp_manager_window import MCPManagerWindow

# Configure logging
logger = logging.getLogger(__name__)

class AsyncTk(tk.Tk):
    """Tkinter main loop with asyncio support."""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.running = True
        self.protocol("WM_DELETE_WINDOW", self.on_close)
        
        # Set up the asyncio task
        self.tasks = []
        self._loop = None
        
    def schedule_task(self, coro: Coroutine[Any, Any, Any]) -> None:
        """Schedule an asyncio task to run in the background."""
        logger.debug(f"schedule_task: Scheduling new task for coroutine: {coro.__qualname__}")
        
        try:
            # Get or create the event loop
            try:
                loop = asyncio.get_running_loop()
                logger.debug(f"schedule_task: Using existing running loop: {id(loop)}")
            except RuntimeError:
                try:
                    loop = asyncio.get_event_loop()
                    logger.debug(f"schedule_task: Using existing event loop: {id(loop)}")
                except RuntimeError:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    logger.debug(f"schedule_task: Created new event loop: {id(loop)}")
            
            self._loop = loop
            
            # Create the task
            def run_coro():
                """Run the coroutine and handle its completion."""
                task = asyncio.ensure_future(coro, loop=loop)
                self.tasks.append(task)
                
                def task_done(future):
                    try:
                        future.result()
                        logger.debug(f"schedule_task: Task completed successfully")
                    except asyncio.CancelledError:
                        logger.debug(f"schedule_task: Task was cancelled")
                    except Exception as e:
                        logger.error(f"schedule_task: Task failed with error: {e}", exc_info=True)
                    finally:
                        if future in self.tasks:
                            self.tasks.remove(future)
                
                task.add_done_callback(task_done)
                return task
            
            # Schedule the task to run after the current call stack
            self.after_idle(lambda: run_coro())
            
            logger.debug(f"schedule_task: Task scheduled successfully")
            
        except Exception as e:
            logger.error(f"schedule_task: Failed to schedule task: {e}", exc_info=True)
            raise
    
    def on_close(self) -> None:
        """Handle window close event."""
        logger.info("Window close requested, cleaning up...")
        self.running = False
        
        # Cancel all running tasks
        for task in self.tasks[:]:  # Copy list to avoid modification during iteration
            if not task.done():
                logger.debug(f"Cancelling task: {id(task)}")
                task.cancel()
        
        # Destroy the window
        self.quit()
        self.destroy()
        logger.info("Window closed")
    
    def run(self):
        """Run the Tkinter main loop."""
        logger.info("Starting application main loop")
        
        # Set up signal handlers for clean shutdown
        import signal
        def handle_signal(signum, frame):
            logger.info(f"Received signal {signum}, shutting down...")
            self.on_close()
        
        signal.signal(signal.SIGINT, handle_signal)
        signal.signal(signal.SIGTERM, handle_signal)
        
        try:
            # Run the Tkinter main loop
            logger.debug("Starting Tkinter main loop")
            self.mainloop()
            
        except Exception as e:
            logger.error(f"Error in main loop: {e}", exc_info=True)
            
        finally:
            logger.info("Application shutdown complete")

class MainWindow(AsyncTk):
    """Main application window."""
    
    def __init__(self, config: Dict[str, Any], assistant: Any):
        """Initialize the main window."""
        super().__init__()
        
        self.config = config
        self.assistant = assistant
        
        # Get app name and version from DEFAULT section
        app_name = self.config.get('DEFAULT', 'app_name', fallback='VoiceFlow AI')
        version = self.config.get('DEFAULT', 'version', fallback='')
        self.title(f"{app_name} {version}")
        
        # Get window dimensions from UI section or use defaults
        width = self.config.getint('UI', 'width', fallback=800)
        height = self.config.getint('UI', 'height', fallback=600)
        min_width = self.config.getint('UI', 'min_width', fallback=600)
        min_height = self.config.getint('UI', 'min_height', fallback=400)
        
        # Configure window size and position
        screen_width = self.winfo_screenwidth()
        screen_height = self.winfo_screenheight()
        
        # Calculate position to center the window
        x = (screen_width // 2) - (width // 2)
        y = (screen_height // 2) - (height // 2)
        
        # Set window geometry (center of screen)
        self.geometry(f"{width}x{height}+{x}+{y}")
        self.minsize(min_width, min_height)
        
        # Set up the UI
        self.setup_ui()
        
        # Initialize state
        self.is_listening = False
        self.current_document = None
    
    def setup_ui(self) -> None:
        """Set up the user interface."""
        # Set up the style
        self.style = ttk.Style(self)
        theme = self.config.get('UI', 'theme', fallback='default')
        try:
            self.style.theme_use(theme)
        except tk.TclError:
            logger.warning(f"Theme '{theme}' not found, using default theme")
            self.style.theme_use('default')
        
        # Main container
        self.main_frame = ttk.Frame(self, padding="10")
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Chat area
        self.setup_chat_area()
        
        # Input area
        self.setup_input_area()
        
        # Status bar
        self.setup_status_bar()
    
    def setup_chat_area(self) -> None:
        """Set up the chat display area."""
        # Chat container
        chat_frame = ttk.LabelFrame(self.main_frame, text="Conversation", padding="5")
        chat_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # Chat history
        self.chat_history = scrolledtext.ScrolledText(
            chat_frame,
            wrap=tk.WORD,
            state=tk.DISABLED,
            font=('Arial', 10)
        )
        self.chat_history.pack(fill=tk.BOTH, expand=True)
        
        # Configure tags for different message types
        self.chat_history.tag_configure('user', foreground='blue')
        self.chat_history.tag_configure('assistant', foreground='green')
        self.chat_history.tag_configure('system', foreground='gray')
        self.chat_history.tag_configure('error', foreground='red')
    
    def setup_input_area(self) -> None:
        """Set up the input area for user messages."""
        # Input frame
        input_frame = ttk.Frame(self.main_frame)
        input_frame.pack(fill=tk.X, pady=(0, 5))
        
        # Text input
        self.input_text = tk.Text(
            input_frame,
            height=4,
            wrap=tk.WORD,
            font=('Arial', 10)
        )
        self.input_text.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))
        self.input_text.bind('<Return>', self.on_enter_pressed)
        self.input_text.bind('<Shift-Return>', lambda e: 'break')  # Prevent newline on Shift+Enter
        
        # Button frame
        button_frame = ttk.Frame(input_frame)
        button_frame.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Send button
        self.send_button = ttk.Button(
            button_frame,
            text="Send",
            command=self.send_message,
            width=8
        )
        self.send_button.pack(side=tk.TOP, fill=tk.X, pady=(0, 5))
        
        # Voice button
        self.voice_button = ttk.Button(
            button_frame,
            text="🎤 Voice",
            command=self.toggle_voice,
            width=8
        )
        self.voice_button.pack(side=tk.TOP, fill=tk.X, pady=(0, 5))
        
        # File button
        self.file_button = ttk.Button(
            button_frame,
            text="📄 File",
            command=self.open_file_dialog,
            width=8
        )
        self.file_button.pack(side=tk.TOP, fill=tk.X, pady=(0, 5))
        
        # Modern Chat button
        self.modern_chat_button = ttk.Button(
            button_frame,
            text="🚀 Modern Chat",
            command=self.open_modern_chat,
            width=8
        )
        self.modern_chat_button.pack(side=tk.TOP, fill=tk.X, pady=(0, 5))
        
        # Provider Status button
        self.provider_status_button = ttk.Button(
            button_frame,
            text="🔌 Providers",
            command=self.open_provider_status,
            width=8
        )
        self.provider_status_button.pack(side=tk.TOP, fill=tk.X, pady=(0, 5))
        
        # MCP Manager button
        self.mcp_manager_button = ttk.Button(
            button_frame,
            text="🛠️ MCP Tools",
            command=self.open_mcp_manager,
            width=8
        )
        self.mcp_manager_button.pack(side=tk.TOP, fill=tk.X)
    
    def setup_status_bar(self) -> None:
        """Set up the status bar."""
        self.status_var = tk.StringVar()
        self.status_var.set("Ready")
        
        status_bar = ttk.Label(
            self.main_frame,
            textvariable=self.status_var,
            relief=tk.SUNKEN,
            anchor=tk.W
        )
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def append_message(self, sender: str, message: str, message_type: str = 'user') -> None:
        """Append a message to the chat history."""
        self.chat_history.config(state=tk.NORMAL)
        
        # Insert sender
        self.chat_history.insert(tk.END, f"{sender}: ", ('bold', message_type))
        
        # Insert message
        self.chat_history.insert(tk.END, f"{message}\n\n", message_type)
        
        # Auto-scroll to bottom
        self.chat_history.see(tk.END)
        self.chat_history.config(state=tk.DISABLED)
    
    def on_enter_pressed(self, event: tk.Event) -> str:
        """Handle Enter key press in the input field."""
        if not event.state & 0x1:  # If Ctrl is not pressed
            self.send_message()
            return 'break'  # Prevent the default newline behavior
        return None
    
    def send_message(self) -> None:
        """Send the current message in the input field."""
        try:
            logger.info("send_message: Starting to process user message")
            message = self.input_text.get("1.0", tk.END).strip()
            if not message:
                logger.warning("send_message: Empty message, skipping")
                return
            
            logger.info(f"send_message: Processing message: {message[:100]}...")
            
            # Clear input
            self.input_text.delete("1.0", tk.END)
            
            # Display user message
            self.append_message("You", message, 'user')
            
            # Disable send button to prevent multiple requests
            self.send_button.config(state='disabled')
            self.status_var.set("Sending...")
            
            # Process the message asynchronously
            logger.info("send_message: Scheduling process_message task")
            self.schedule_task(self.process_message(message))
            
        except Exception as e:
            error_msg = f"Error in send_message: {e}"
            logger.error(error_msg, exc_info=True)
            self.status_var.set("Error sending message")
            self.send_button.config(state='normal')
    
    async def process_message(self, message: str) -> None:
        """Process a user message and get a response."""
        logger.info(f"process_message: Starting to process message: {message[:100]}...")
        
        try:
            # Show typing indicator
            self.status_var.set("Assistant is thinking...")
            
            # Get response from assistant  
            logger.info("process_message: Calling assistant.process_text_input")
            try:
                # Call the assistant directly - it handles all the async communication
                response = await self.assistant.process_text_input(message)
                
                # Log the response
                logger.info(f"process_message: Received response of length: {len(str(response))}")
                logger.debug(f"process_message: Response content: {str(response)[:200]}{'...' if len(str(response)) > 200 else ''}")
                
                # Update the UI with the response (use after to ensure thread safety)
                self.after(0, lambda: self.append_message("Assistant", str(response), 'assistant'))
                self.after(0, lambda: self.status_var.set("Ready"))
                logger.info("process_message: Message processing completed successfully")
                    
            except asyncio.TimeoutError:
                logger.error("process_message: Timeout waiting for assistant response")
                self.after(0, lambda: self.append_message("System", "Error: Request timed out", 'error'))
                self.after(0, lambda: self.status_var.set("Error: Request timed out"))
                    
            except Exception as e:
                logger.error(f"process_message: Error in assistant.process_text_input: {e}", exc_info=True)
                self.after(0, lambda: self.append_message("System", f"Error: {str(e)}", 'error'))
                self.after(0, lambda: self.status_var.set("Error processing message"))
                
        except Exception as e:
            error_msg = f"process_message: Unexpected error: {e}"
            logger.error(error_msg, exc_info=True)
            self.after(0, lambda: self.append_message("System", f"Unexpected error: {str(e)}", 'error'))
            self.after(0, lambda: self.status_var.set("Error processing message"))
            
        finally:
            # Re-enable the send button
            self.after(0, lambda: self.send_button.config(state='normal'))
    
    def toggle_voice(self) -> None:
        """Toggle voice input mode."""
        if not self.is_listening:
            self.start_voice_input()
        else:
            self.stop_voice_input()
    
    def start_voice_input(self) -> None:
        """Start listening for voice input."""
        self.is_listening = True
        self.voice_button.config(text="🔴 Listening...")
        self.status_var.set("Listening... (say 'hey assistant' to start)")
        
        # Start listening in the background
        self.schedule_task(self.process_voice_input())
    
    async def process_voice_input(self) -> None:
        """Process voice input."""
        try:
            while self.is_listening:
                try:
                    # Listen for voice input
                    text = await self.assistant.process_voice_input()
                    
                    if text:
                        # Update UI on the main thread
                        self.after(0, lambda t=text: self.append_message("You (Voice)", t, 'user'))
                        
                        # Process the recognized text
                        response = await self.assistant.process_text_input(text)
                        self.after(0, lambda r=response: self.append_message("Assistant", r, 'assistant'))
                    
                except Exception as e:
                    logger.error(f"Error in voice input: {e}", exc_info=True)
                    self.after(0, lambda: self.status_var.set(f"Voice input error: {str(e)}"))
                    await asyncio.sleep(1)  # Prevent tight loop on error
        finally:
            self.after(0, self.stop_voice_input)
    
    def stop_voice_input(self) -> None:
        """Stop listening for voice input."""
        self.is_listening = False
        self.voice_button.config(text="🎤 Voice")
        self.status_var.set("Ready")
    
    def open_file_dialog(self) -> None:
        """Open a file dialog to select a document."""
        filetypes = [
            ("All supported files", "*.txt *.pdf *.docx *.doc *.jpg *.jpeg *.png"),
            ("Text files", "*.txt"),
            ("PDF files", "*.pdf"),
            ("Word documents", "*.docx *.doc"),
            ("Image files", "*.jpg *.jpeg *.png"),
            ("All files", "*.*")
        ]
        
        file_path = filedialog.askopenfilename(
            title="Select a document",
            filetypes=filetypes
        )
        
        if file_path:
            self.schedule_task(self.process_document(file_path))
    
    async def process_document(self, file_path: str) -> None:
        """Process a document file."""
        try:
            self.status_var.set(f"Processing document: {Path(file_path).name}...")
            
            # Process the document
            response = await self.assistant.process_document(file_path)
            
            # Display the response
            self.append_message("Document", f"Processed: {Path(file_path).name}", 'system')
            self.append_message("Assistant", response, 'assistant')
            
            self.status_var.set("Ready")
            
        except Exception as e:
            logger.error(f"Error processing document: {e}", exc_info=True)
            self.append_message("Error", f"Failed to process document: {str(e)}", 'error')
            self.status_var.set("Error processing document")
    
    def open_modern_chat(self) -> None:
        """Open the modern chat window."""
        try:
            # Create and show the modern chat window
            modern_chat = ModernChatWindow(self, self.assistant, self.config)
            modern_chat.focus_set()  # Give focus to the new window
            logger.info("Modern chat window opened")
        except Exception as e:
            logger.error(f"Error opening modern chat: {e}", exc_info=True)
            messagebox.showerror("Error", f"Failed to open modern chat: {str(e)}")
    
    def open_provider_status(self) -> None:
        """Open the provider status window."""
        try:
            # Create and show the provider status window
            provider_status = ProviderStatusWindow(self, self.config)
            provider_status.focus_set()  # Give focus to the new window
            logger.info("Provider status window opened")
        except Exception as e:
            logger.error(f"Error opening provider status: {e}", exc_info=True)
            messagebox.showerror("Error", f"Failed to open provider status: {str(e)}")
    
    def open_mcp_manager(self) -> None:
        """Open the MCP manager window."""
        try:
            # Create and show the MCP manager window
            mcp_manager = MCPManagerWindow(self, self.config)
            mcp_manager.focus_set()  # Give focus to the new window
            logger.info("MCP manager window opened")
        except Exception as e:
            logger.error(f"Error opening MCP manager: {e}", exc_info=True)
            messagebox.showerror("Error", f"Failed to open MCP manager: {str(e)}")
