"""
Theme system for Cherry Studio Clone.
"""
from typing import Dict, Any, List
from dataclasses import dataclass
import json
from pathlib import Path

@dataclass
class Theme:
    """Represents a UI theme."""
    name: str
    display_name: str
    description: str
    colors: Dict[str, str]
    is_dark: bool = True
    is_builtin: bool = True

class ThemeManager:
    """Manages UI themes for the application."""
    
    def __init__(self, data_dir: str = "data"):
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(exist_ok=True)
        self.themes_file = self.data_dir / "themes.json"
        
        self.themes: Dict[str, Theme] = {}
        self.current_theme_name = "company_theme"
        
        self.create_builtin_themes()
        self.load_custom_themes()
    
    def create_builtin_themes(self):
        """Create built-in themes."""
        
        # Company Theme (Pink, Baby Blue, White)
        company_theme = Theme(
            name="company_theme",
            display_name="Company Theme",
            description="Pink, baby blue, and white company colors",
            colors={
                'bg_primary': '#ffffff',        # White background
                'bg_secondary': '#f8f9fa',      # Very light gray
                'bg_tertiary': '#e9ecef',       # Light gray
                'text_primary': '#212529',      # Dark text
                'text_secondary': '#6c757d',    # Gray text
                'accent': '#87CEEB',            # Baby blue accent
                'accent_hover': '#6BB6E8',      # Darker baby blue
                'success': '#28a745',           # Green
                'warning': '#ffc107',           # Yellow
                'danger': '#dc3545',            # Red
                'user_msg': '#FFB6C1',          # Light pink for user messages
                'assistant_msg': '#E6F3FF',     # Very light blue for assistant messages
                'pink_accent': '#FF69B4',       # Hot pink accent
                'blue_accent': '#87CEEB',       # Sky blue accent
                'border': '#dee2e6',            # Light border
                'hover': '#f1f3f4',             # Hover state
            },
            is_dark=False
        )
        
        # Dark Company Theme
        dark_company_theme = Theme(
            name="dark_company_theme",
            display_name="Dark Company Theme",
            description="Dark theme with pink and baby blue accents",
            colors={
                'bg_primary': '#1a1a1a',        # Dark background
                'bg_secondary': '#2d2d2d',      # Slightly lighter
                'bg_tertiary': '#3d3d3d',       # Even lighter
                'text_primary': '#ffffff',      # White text
                'text_secondary': '#b0b0b0',    # Gray text
                'accent': '#87CEEB',            # Baby blue accent
                'accent_hover': '#6BB6E8',      # Darker baby blue
                'success': '#28a745',           # Green
                'warning': '#ffc107',           # Yellow
                'danger': '#dc3545',            # Red
                'user_msg': '#FF69B4',          # Hot pink for user messages
                'assistant_msg': '#E6F3FF',     # Very light blue for assistant messages
                'pink_accent': '#FF69B4',       # Hot pink accent
                'blue_accent': '#87CEEB',       # Sky blue accent
                'border': '#495057',            # Dark border
                'hover': '#495057',             # Hover state
            },
            is_dark=True
        )
        
        # Cherry Studio Classic (similar to original)
        cherry_classic_theme = Theme(
            name="cherry_classic",
            display_name="Cherry Classic",
            description="Classic Cherry Studio inspired theme",
            colors={
                'bg_primary': '#1e1e1e',        # Dark background
                'bg_secondary': '#2d2d30',      # Slightly lighter
                'bg_tertiary': '#3e3e42',       # Even lighter
                'text_primary': '#cccccc',      # Light text
                'text_secondary': '#969696',    # Gray text
                'accent': '#007acc',            # Blue accent
                'accent_hover': '#005a9e',      # Darker blue
                'success': '#28a745',           # Green
                'warning': '#ffc107',           # Yellow
                'danger': '#dc3545',            # Red
                'user_msg': '#0084ff',          # Blue for user messages
                'assistant_msg': '#f0f0f0',     # Light gray for assistant messages
                'pink_accent': '#ff6b9d',       # Pink accent
                'blue_accent': '#007acc',       # Blue accent
                'border': '#464647',            # Border
                'hover': '#464647',             # Hover state
            },
            is_dark=True
        )
        
        # Light Theme
        light_theme = Theme(
            name="light_theme",
            display_name="Light Theme",
            description="Clean light theme",
            colors={
                'bg_primary': '#ffffff',        # White background
                'bg_secondary': '#f8f9fa',      # Very light gray
                'bg_tertiary': '#e9ecef',       # Light gray
                'text_primary': '#212529',      # Dark text
                'text_secondary': '#6c757d',    # Gray text
                'accent': '#007bff',            # Blue accent
                'accent_hover': '#0056b3',      # Darker blue
                'success': '#28a745',           # Green
                'warning': '#ffc107',           # Yellow
                'danger': '#dc3545',            # Red
                'user_msg': '#007bff',          # Blue for user messages
                'assistant_msg': '#f8f9fa',     # Light gray for assistant messages
                'pink_accent': '#e83e8c',       # Pink accent
                'blue_accent': '#007bff',       # Blue accent
                'border': '#dee2e6',            # Light border
                'hover': '#f1f3f4',             # Hover state
            },
            is_dark=False
        )
        
        self.themes = {
            theme.name: theme for theme in [
                company_theme, dark_company_theme, cherry_classic_theme, light_theme
            ]
        }
    
    def load_custom_themes(self):
        """Load custom themes from file."""
        try:
            if self.themes_file.exists():
                with open(self.themes_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    for theme_data in data:
                        theme = Theme(**theme_data)
                        if not theme.is_builtin:  # Only load custom themes
                            self.themes[theme.name] = theme
        except Exception as e:
            print(f"Error loading custom themes: {e}")
    
    def save_custom_themes(self):
        """Save custom themes to file."""
        try:
            custom_themes = [
                theme for theme in self.themes.values()
                if not theme.is_builtin
            ]
            
            with open(self.themes_file, 'w', encoding='utf-8') as f:
                data = [
                    {
                        'name': theme.name,
                        'display_name': theme.display_name,
                        'description': theme.description,
                        'colors': theme.colors,
                        'is_dark': theme.is_dark,
                        'is_builtin': theme.is_builtin
                    }
                    for theme in custom_themes
                ]
                json.dump(data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"Error saving custom themes: {e}")
    
    def get_theme(self, theme_name: str) -> Theme:
        """Get a theme by name."""
        return self.themes.get(theme_name, self.themes['company_theme'])
    
    def get_all_themes(self) -> List[Theme]:
        """Get all available themes."""
        return list(self.themes.values())
    
    def get_theme_names(self) -> List[str]:
        """Get all theme names."""
        return [theme.display_name for theme in self.themes.values()]
    
    def set_current_theme(self, theme_name: str):
        """Set the current theme."""
        if theme_name in self.themes:
            self.current_theme_name = theme_name
    
    def get_current_theme(self) -> Theme:
        """Get the current theme."""
        return self.get_theme(self.current_theme_name)
    
    def create_custom_theme(self, name: str, display_name: str, description: str, 
                           colors: Dict[str, str], is_dark: bool = True) -> Theme:
        """Create a custom theme."""
        theme = Theme(
            name=name,
            display_name=display_name,
            description=description,
            colors=colors,
            is_dark=is_dark,
            is_builtin=False
        )
        
        self.themes[name] = theme
        self.save_custom_themes()
        return theme
    
    def delete_custom_theme(self, theme_name: str) -> bool:
        """Delete a custom theme."""
        if theme_name in self.themes and not self.themes[theme_name].is_builtin:
            del self.themes[theme_name]
            self.save_custom_themes()
            return True
        return False
