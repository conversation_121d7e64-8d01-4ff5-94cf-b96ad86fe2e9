"""
Modern chat window for <PERSON><PERSON>.
"""
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog
import asyncio
import logging
from pathlib import Path
from typing import Optional, Dict, Any, List, Callable
import json
import datetime
from dataclasses import dataclass, asdict
import uuid

from ..assistants import <PERSON><PERSON><PERSON><PERSON>, Assistant<PERSON>rofile
from .themes import ThemeManager

logger = logging.getLogger(__name__)

@dataclass
class Message:
    """Represents a chat message."""
    id: str
    role: str  # 'user', 'assistant', 'system'
    content: str
    timestamp: str
    model: Optional[str] = None
    tokens: Optional[int] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Message':
        return cls(**data)

@dataclass
class Conversation:
    """Represents a conversation/topic."""
    id: str
    title: str
    messages: List[Message]
    created_at: str
    updated_at: str
    model: Optional[str] = None
    assistant_id: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'id': self.id,
            'title': self.title,
            'messages': [msg.to_dict() for msg in self.messages],
            'created_at': self.created_at,
            'updated_at': self.updated_at,
            'model': self.model,
            'assistant_id': self.assistant_id
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Conversation':
        messages = [Message.from_dict(msg) for msg in data.get('messages', [])]
        return cls(
            id=data['id'],
            title=data['title'],
            messages=messages,
            created_at=data['created_at'],
            updated_at=data['updated_at'],
            model=data.get('model'),
            assistant_id=data.get('assistant_id')
        )

class ModernChatWindow(tk.Toplevel):
    """Modern chat window for Augie."""
    
    def __init__(self, parent, assistant, config):
        super().__init__(parent)
        self.parent = parent
        self.assistant = assistant
        self.config = config
        
        # Window configuration
        self.title("Augie - Chat")
        self.geometry("1200x800")
        self.minsize(800, 600)
        
        # State
        self.current_conversation: Optional[Conversation] = None
        self.conversations: List[Conversation] = []
        self.current_model = "gpt-3.5-turbo"
        self.is_sending = False
        
        # Initialize managers
        self.assistant_manager = AssistantManager()
        self.current_assistant_profile: Optional[AssistantProfile] = None
        self.theme_manager = ThemeManager()
        
        # Load conversations from file
        self.load_conversations()
        
        # Set up the UI
        self.setup_styles()
        self.setup_ui()
        
        # Create initial conversation if none exist
        if not self.conversations:
            self.new_conversation()
        else:
            self.current_conversation = self.conversations[0]
            self.refresh_chat_display()
    
    def setup_styles(self):
        """Set up custom styles for the modern interface."""
        self.style = ttk.Style()
        
        # Get current theme colors
        current_theme = self.theme_manager.get_current_theme()
        self.colors = current_theme.colors
        
        # Configure the window
        self.configure(bg=self.colors['bg_primary'])
        
        # Configure ttk styles
        self.style.theme_use('clam')
        
        # Custom button style
        self.style.configure(
            'Modern.TButton',
            background=self.colors['accent'],
            foreground='white',
            borderwidth=0,
            focuscolor='none',
            padding=(10, 5)
        )
        self.style.map(
            'Modern.TButton',
            background=[('active', self.colors['accent_hover'])]
        )
        
        # Custom frame style
        self.style.configure(
            'Modern.TFrame',
            background=self.colors['bg_secondary'],
            borderwidth=0
        )
        
        # Custom label style
        self.style.configure(
            'Modern.TLabel',
            background=self.colors['bg_secondary'],
            foreground=self.colors['text_primary']
        )
    
    def setup_ui(self):
        """Set up the modern user interface."""
        # Main container
        self.main_frame = tk.Frame(self, bg=self.colors['bg_primary'])
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create the three-panel layout
        self.setup_sidebar()
        self.setup_chat_area()
        self.setup_right_panel()
    
    def setup_sidebar(self):
        """Set up the left sidebar with conversations."""
        # Sidebar frame
        self.sidebar = tk.Frame(
            self.main_frame,
            bg=self.colors['bg_secondary'],
            width=250
        )
        self.sidebar.pack(side=tk.LEFT, fill=tk.Y)
        self.sidebar.pack_propagate(False)
        
        # Sidebar header
        header_frame = tk.Frame(self.sidebar, bg=self.colors['bg_secondary'])
        header_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # New chat button
        self.new_chat_btn = tk.Button(
            header_frame,
            text="+ New Chat",
            bg=self.colors['accent'],
            fg='white',
            border=0,
            font=('Arial', 10, 'bold'),
            command=self.new_conversation,
            cursor='hand2'
        )
        self.new_chat_btn.pack(fill=tk.X, pady=(0, 10))
        
        # Conversations list
        self.setup_conversations_list()
    
    def setup_conversations_list(self):
        """Set up the conversations list in the sidebar."""
        # Conversations frame with scrollbar
        list_frame = tk.Frame(self.sidebar, bg=self.colors['bg_secondary'])
        list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))
        
        # Canvas for scrolling
        self.conv_canvas = tk.Canvas(
            list_frame,
            bg=self.colors['bg_secondary'],
            highlightthickness=0
        )
        self.conv_scrollbar = ttk.Scrollbar(
            list_frame,
            orient="vertical",
            command=self.conv_canvas.yview
        )
        self.conv_scrollable_frame = tk.Frame(
            self.conv_canvas,
            bg=self.colors['bg_secondary']
        )
        
        self.conv_scrollable_frame.bind(
            "<Configure>",
            lambda e: self.conv_canvas.configure(scrollregion=self.conv_canvas.bbox("all"))
        )
        
        self.conv_canvas.create_window((0, 0), window=self.conv_scrollable_frame, anchor="nw")
        self.conv_canvas.configure(yscrollcommand=self.conv_scrollbar.set)
        
        self.conv_canvas.pack(side="left", fill="both", expand=True)
        self.conv_scrollbar.pack(side="right", fill="y")
        
        # Refresh the conversations display
        self.refresh_conversations_list()
    
    def setup_chat_area(self):
        """Set up the main chat area."""
        # Chat area frame
        self.chat_frame = tk.Frame(
            self.main_frame,
            bg=self.colors['bg_primary']
        )
        self.chat_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # Chat header
        self.setup_chat_header()
        
        # Messages area
        self.setup_messages_area()
        
        # Input area
        self.setup_input_area()
    
    def setup_chat_header(self):
        """Set up the chat header with model selector."""
        header_frame = tk.Frame(
            self.chat_frame,
            bg=self.colors['bg_secondary'],
            height=60
        )
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)
        
        # Title
        self.chat_title = tk.Label(
            header_frame,
            text="New Conversation",
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_primary'],
            font=('Arial', 14, 'bold')
        )
        self.chat_title.pack(side=tk.LEFT, padx=20, pady=15)
        
        # Model selector
        model_frame = tk.Frame(header_frame, bg=self.colors['bg_secondary'])
        model_frame.pack(side=tk.RIGHT, padx=20, pady=15)
        
        tk.Label(
            model_frame,
            text="Model:",
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_secondary'],
            font=('Arial', 10)
        ).pack(side=tk.LEFT, padx=(0, 5))
        
        self.model_var = tk.StringVar(value=self.current_model)
        self.model_selector = ttk.Combobox(
            model_frame,
            textvariable=self.model_var,
            values=self.get_available_models(),
            state="readonly",
            width=20
        )
        self.model_selector.pack(side=tk.LEFT)
        self.model_selector.bind('<<ComboboxSelected>>', self.on_model_changed)
    
    def setup_messages_area(self):
        """Set up the messages display area."""
        # Messages container
        messages_frame = tk.Frame(self.chat_frame, bg=self.colors['bg_primary'])
        messages_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # Canvas for scrolling messages
        self.messages_canvas = tk.Canvas(
            messages_frame,
            bg=self.colors['bg_primary'],
            highlightthickness=0
        )
        self.messages_scrollbar = ttk.Scrollbar(
            messages_frame,
            orient="vertical",
            command=self.messages_canvas.yview
        )
        self.messages_scrollable_frame = tk.Frame(
            self.messages_canvas,
            bg=self.colors['bg_primary']
        )
        
        self.messages_scrollable_frame.bind(
            "<Configure>",
            lambda e: self.messages_canvas.configure(scrollregion=self.messages_canvas.bbox("all"))
        )
        
        self.messages_canvas.create_window((0, 0), window=self.messages_scrollable_frame, anchor="nw")
        self.messages_canvas.configure(yscrollcommand=self.messages_scrollbar.set)
        
        self.messages_canvas.pack(side="left", fill="both", expand=True)
        self.messages_scrollbar.pack(side="right", fill="y")
        
        # Bind mouse wheel to canvas
        self.messages_canvas.bind("<MouseWheel>", self._on_mousewheel)
    
    def setup_input_area(self):
        """Set up the message input area."""
        # Input container
        input_container = tk.Frame(
            self.chat_frame,
            bg=self.colors['bg_secondary'],
            height=100
        )
        input_container.pack(fill=tk.X, padx=20, pady=(0, 20))
        input_container.pack_propagate(False)
        
        # Input frame
        input_frame = tk.Frame(input_container, bg=self.colors['bg_secondary'])
        input_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Text input
        self.input_text = tk.Text(
            input_frame,
            height=3,
            wrap=tk.WORD,
            font=('Arial', 11),
            bg=self.colors['bg_tertiary'],
            fg=self.colors['text_primary'],
            insertbackground=self.colors['text_primary'],
            border=0,
            relief=tk.FLAT
        )
        self.input_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # Bind events
        self.input_text.bind('<Return>', self.on_enter_pressed)
        self.input_text.bind('<Shift-Return>', lambda e: None)  # Allow Shift+Enter for newlines
        
        # Buttons frame
        buttons_frame = tk.Frame(input_frame, bg=self.colors['bg_secondary'])
        buttons_frame.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Send button
        self.send_btn = tk.Button(
            buttons_frame,
            text="Send",
            bg=self.colors['accent'],
            fg='white',
            border=0,
            font=('Arial', 10, 'bold'),
            command=self.send_message,
            cursor='hand2',
            width=8
        )
        self.send_btn.pack(pady=(0, 5))
        
        # File button
        self.file_btn = tk.Button(
            buttons_frame,
            text="📎",
            bg=self.colors['bg_tertiary'],
            fg=self.colors['text_primary'],
            border=0,
            font=('Arial', 12),
            command=self.attach_file,
            cursor='hand2',
            width=8
        )
        self.file_btn.pack()
    
    def setup_right_panel(self):
        """Set up the right panel for settings and info."""
        # Right panel frame
        self.right_panel = tk.Frame(
            self.main_frame,
            bg=self.colors['bg_secondary'],
            width=200
        )
        self.right_panel.pack(side=tk.RIGHT, fill=tk.Y)
        self.right_panel.pack_propagate(False)
        
        # Panel header
        header = tk.Label(
            self.right_panel,
            text="Settings",
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_primary'],
            font=('Arial', 12, 'bold')
        )
        header.pack(pady=20)
        
        # Assistant selector
        self.setup_assistant_selector()
        
        # Model info
        self.setup_model_info()
        
        # Theme selector
        self.setup_theme_selector()
        
        # Conversation stats
        self.setup_conversation_stats()
    
    def setup_assistant_selector(self):
        """Set up the assistant selector."""
        assistant_frame = tk.Frame(self.right_panel, bg=self.colors['bg_secondary'])
        assistant_frame.pack(fill=tk.X, padx=10, pady=10)
        
        tk.Label(
            assistant_frame,
            text="Assistant",
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_secondary'],
            font=('Arial', 10, 'bold')
        ).pack(anchor=tk.W)
        
        # Get assistant options
        assistants = self.assistant_manager.get_all_assistants()
        assistant_options = [f"{assistant.name}" for assistant in assistants]
        
        # Set default assistant
        if not self.current_assistant_profile and assistants:
            self.current_assistant_profile = assistants[0]
        
        current_name = self.current_assistant_profile.name if self.current_assistant_profile else "General Assistant"
        
        self.assistant_var = tk.StringVar(value=current_name)
        self.assistant_selector = ttk.Combobox(
            assistant_frame,
            textvariable=self.assistant_var,
            values=assistant_options,
            state="readonly",
            width=18
        )
        self.assistant_selector.pack(fill=tk.X, pady=(5, 0))
        self.assistant_selector.bind('<<ComboboxSelected>>', self.on_assistant_changed)
        
        # Assistant description
        if self.current_assistant_profile:
            description = self.current_assistant_profile.description[:60] + ("..." if len(self.current_assistant_profile.description) > 60 else "")
        else:
            description = "No assistant selected"
            
        self.assistant_desc_label = tk.Label(
            assistant_frame,
            text=description,
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_secondary'],
            font=('Arial', 8),
            wraplength=180,
            justify=tk.LEFT
        )
        self.assistant_desc_label.pack(anchor=tk.W, pady=(5, 0))
    
    def setup_model_info(self):
        """Set up model information display."""
        info_frame = tk.Frame(self.right_panel, bg=self.colors['bg_secondary'])
        info_frame.pack(fill=tk.X, padx=10, pady=10)
        
        tk.Label(
            info_frame,
            text="Current Model",
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_secondary'],
            font=('Arial', 10, 'bold')
        ).pack(anchor=tk.W)
        
        self.model_info_label = tk.Label(
            info_frame,
            text=self.current_model,
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_primary'],
            font=('Arial', 10)
        )
        self.model_info_label.pack(anchor=tk.W, pady=(5, 0))
    
    def setup_conversation_stats(self):
        """Set up conversation statistics display."""
        stats_frame = tk.Frame(self.right_panel, bg=self.colors['bg_secondary'])
        stats_frame.pack(fill=tk.X, padx=10, pady=20)
        
        tk.Label(
            stats_frame,
            text="Conversation Stats",
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_secondary'],
            font=('Arial', 10, 'bold')
        ).pack(anchor=tk.W)
        
        self.stats_label = tk.Label(
            stats_frame,
            text="Messages: 0\nTokens: 0",
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_primary'],
            font=('Arial', 9),
            justify=tk.LEFT
        )
        self.stats_label.pack(anchor=tk.W, pady=(5, 0))
    
    def setup_theme_selector(self):
        """Set up the theme selector."""
        theme_frame = tk.Frame(self.right_panel, bg=self.colors['bg_secondary'])
        theme_frame.pack(fill=tk.X, padx=10, pady=10)
        
        tk.Label(
            theme_frame,
            text="Theme",
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_secondary'],
            font=('Arial', 10, 'bold')
        ).pack(anchor=tk.W)
        
        # Get theme options
        theme_names = self.theme_manager.get_theme_names()
        current_theme = self.theme_manager.get_current_theme()
        
        self.theme_var = tk.StringVar(value=current_theme.display_name)
        self.theme_selector = ttk.Combobox(
            theme_frame,
            textvariable=self.theme_var,
            values=theme_names,
            state="readonly",
            width=18
        )
        self.theme_selector.pack(fill=tk.X, pady=(5, 0))
        self.theme_selector.bind('<<ComboboxSelected>>', self.on_theme_changed)
    
    def get_available_models(self) -> List[str]:
        """Get list of available models from all providers."""
        models = []
        
        # OpenAI models
        models.extend([
            "gpt-4o",
            "gpt-4o-mini", 
            "gpt-4-turbo",
            "gpt-4",
            "gpt-3.5-turbo"
        ])
        
        # Anthropic models
        models.extend([
            "claude-3-5-sonnet-20241022",
            "claude-3-5-haiku-20241022",
            "claude-3-opus-20240229",
            "claude-3-sonnet-20240229",
            "claude-3-haiku-20240307"
        ])
        
        # Google models
        models.extend([
            "gemini-1.5-pro",
            "gemini-1.5-flash",
            "gemini-pro"
        ])
        
        # Mistral models
        models.extend([
            "mistral-large-latest",
            "mistral-medium-latest",
            "mistral-small-latest",
            "codestral-latest"
        ])
        
        # Cohere models
        models.extend([
            "command-r-plus",
            "command-r",
            "command"
        ])
        
        # Groq models (ultra-fast)
        models.extend([
            "llama-3.1-70b-versatile",
            "llama-3.1-8b-instant",
            "mixtral-8x7b-32768"
        ])
        
        # Perplexity models (with web search)
        models.extend([
            "llama-3.1-sonar-large-128k-online",
            "llama-3.1-sonar-small-128k-online"
        ])
        
        # Local models
        models.extend([
            "ollama/llama3.1:8b",
            "ollama/llama3.1:70b",
            "ollama/mistral:7b",
            "ollama/codellama:7b",
            "lmstudio/local-model",
            "vllm/local-model"
        ])
        
        return models
    
    def new_conversation(self):
        """Create a new conversation."""
        conv_id = str(uuid.uuid4())
        timestamp = datetime.datetime.now().isoformat()
        
        conversation = Conversation(
            id=conv_id,
            title="New Conversation",
            messages=[],
            created_at=timestamp,
            updated_at=timestamp,
            model=self.current_model
        )
        
        self.conversations.insert(0, conversation)
        self.current_conversation = conversation
        
        # Update UI
        self.refresh_conversations_list()
        self.refresh_chat_display()
        self.save_conversations()
    
    def refresh_conversations_list(self):
        """Refresh the conversations list in the sidebar."""
        # Clear existing conversation widgets
        for widget in self.conv_scrollable_frame.winfo_children():
            widget.destroy()
        
        # Add conversation items
        for i, conv in enumerate(self.conversations):
            self.create_conversation_item(conv, i)
    
    def create_conversation_item(self, conversation: Conversation, index: int):
        """Create a conversation item widget."""
        # Conversation frame
        conv_frame = tk.Frame(
            self.conv_scrollable_frame,
            bg=self.colors['bg_tertiary'] if conversation == self.current_conversation else self.colors['bg_secondary'],
            cursor='hand2'
        )
        conv_frame.pack(fill=tk.X, pady=2)
        
        # Title
        title_label = tk.Label(
            conv_frame,
            text=conversation.title[:30] + ("..." if len(conversation.title) > 30 else ""),
            bg=conv_frame['bg'],
            fg=self.colors['text_primary'],
            font=('Arial', 10, 'bold' if conversation == self.current_conversation else 'normal'),
            anchor=tk.W
        )
        title_label.pack(fill=tk.X, padx=10, pady=(5, 0))
        
        # Last message preview
        if conversation.messages:
            last_msg = conversation.messages[-1]
            preview = last_msg.content[:50] + ("..." if len(last_msg.content) > 50 else "")
        else:
            preview = "No messages yet"
        
        preview_label = tk.Label(
            conv_frame,
            text=preview,
            bg=conv_frame['bg'],
            fg=self.colors['text_secondary'],
            font=('Arial', 9),
            anchor=tk.W
        )
        preview_label.pack(fill=tk.X, padx=10, pady=(0, 5))
        
        # Bind click events
        def on_click(event, conv=conversation):
            self.select_conversation(conv)
        
        conv_frame.bind("<Button-1>", on_click)
        title_label.bind("<Button-1>", on_click)
        preview_label.bind("<Button-1>", on_click)
    
    def select_conversation(self, conversation: Conversation):
        """Select a conversation."""
        self.current_conversation = conversation
        self.refresh_conversations_list()
        self.refresh_chat_display()
    
    def refresh_chat_display(self):
        """Refresh the chat display area."""
        if not self.current_conversation:
            return
        
        # Update title
        self.chat_title.config(text=self.current_conversation.title)
        
        # Clear messages
        for widget in self.messages_scrollable_frame.winfo_children():
            widget.destroy()
        
        # Add messages
        for message in self.current_conversation.messages:
            self.add_message_widget(message)
        
        # Update stats
        self.update_conversation_stats()
        
        # Scroll to bottom
        self.after(100, self._scroll_to_bottom)
    
    def add_message_widget(self, message: Message):
        """Add a message widget to the chat display."""
        # Message container
        msg_container = tk.Frame(
            self.messages_scrollable_frame,
            bg=self.colors['bg_primary']
        )
        msg_container.pack(fill=tk.X, pady=5)
        
        # Determine alignment and colors
        if message.role == 'user':
            anchor = tk.E
            bg_color = self.colors['user_msg']
            text_color = 'white'
            padx = (100, 0)
        else:
            anchor = tk.W
            bg_color = self.colors['assistant_msg']
            text_color = 'black'
            padx = (0, 100)
        
        # Message bubble
        bubble_frame = tk.Frame(msg_container, bg=self.colors['bg_primary'])
        bubble_frame.pack(anchor=anchor, padx=padx)
        
        # Message content
        msg_label = tk.Label(
            bubble_frame,
            text=message.content,
            bg=bg_color,
            fg=text_color,
            font=('Arial', 10),
            wraplength=400,
            justify=tk.LEFT,
            anchor=tk.W
        )
        msg_label.pack(padx=15, pady=10)
        
        # Timestamp
        timestamp = datetime.datetime.fromisoformat(message.timestamp).strftime("%H:%M")
        time_label = tk.Label(
            msg_container,
            text=timestamp,
            bg=self.colors['bg_primary'],
            fg=self.colors['text_secondary'],
            font=('Arial', 8)
        )
        time_label.pack(anchor=anchor, padx=padx)
    
    def on_enter_pressed(self, event):
        """Handle Enter key press."""
        if not (event.state & 0x1):  # If Shift is not pressed
            self.send_message()
            return 'break'
        return None
    
    def send_message(self):
        """Send a message."""
        if self.is_sending:
            return
        
        message_text = self.input_text.get("1.0", tk.END).strip()
        if not message_text:
            return
        
        # Clear input
        self.input_text.delete("1.0", tk.END)
        
        # Create user message
        user_message = Message(
            id=str(uuid.uuid4()),
            role='user',
            content=message_text,
            timestamp=datetime.datetime.now().isoformat()
        )
        
        # Add to conversation
        if not self.current_conversation:
            self.new_conversation()
        
        self.current_conversation.messages.append(user_message)
        self.current_conversation.updated_at = datetime.datetime.now().isoformat()
        
        # Update title if this is the first message
        if len(self.current_conversation.messages) == 1:
            title = message_text[:30] + ("..." if len(message_text) > 30 else "")
            self.current_conversation.title = title
        
        # Update UI
        self.add_message_widget(user_message)
        self.refresh_conversations_list()
        self.update_conversation_stats()
        
        # Send to assistant
        self.is_sending = True
        self.send_btn.config(state='disabled', text='Sending...')
        
        # Schedule async processing
        if hasattr(self.parent, 'schedule_task'):
            self.parent.schedule_task(self.process_assistant_response(message_text))
        else:
            # Fallback for testing
            self.after(100, lambda: self.mock_assistant_response(message_text))
        
        self._scroll_to_bottom()
        self.save_conversations()
    
    async def process_assistant_response(self, message_text: str):
        """Process assistant response."""
        try:
            # Get response from assistant
            response = await self.assistant.process_text_input(message_text)
            
            # Create assistant message
            assistant_message = Message(
                id=str(uuid.uuid4()),
                role='assistant',
                content=str(response),
                timestamp=datetime.datetime.now().isoformat(),
                model=self.current_model
            )
            
            # Add to conversation
            self.current_conversation.messages.append(assistant_message)
            self.current_conversation.updated_at = datetime.datetime.now().isoformat()
            
            # Update UI on main thread
            self.after(0, lambda: self.add_message_widget(assistant_message))
            self.after(0, self.update_conversation_stats)
            self.after(0, self._scroll_to_bottom)
            self.after(0, self.save_conversations)
            
        except Exception as e:
            logger.error(f"Error processing assistant response: {e}")
            error_message = Message(
                id=str(uuid.uuid4()),
                role='system',
                content=f"Error: {str(e)}",
                timestamp=datetime.datetime.now().isoformat()
            )
            self.current_conversation.messages.append(error_message)
            self.after(0, lambda: self.add_message_widget(error_message))
        
        finally:
            # Re-enable send button
            self.after(0, lambda: self.send_btn.config(state='normal', text='Send'))
            self.is_sending = False
    
    def mock_assistant_response(self, message_text: str):
        """Mock assistant response for testing."""
        response_text = f"I received your message: '{message_text}'. This is a mock response."
        
        assistant_message = Message(
            id=str(uuid.uuid4()),
            role='assistant',
            content=response_text,
            timestamp=datetime.datetime.now().isoformat(),
            model=self.current_model
        )
        
        self.current_conversation.messages.append(assistant_message)
        self.current_conversation.updated_at = datetime.datetime.now().isoformat()
        
        self.add_message_widget(assistant_message)
        self.update_conversation_stats()
        self._scroll_to_bottom()
        self.save_conversations()
        
        self.send_btn.config(state='normal', text='Send')
        self.is_sending = False
    
    def attach_file(self):
        """Handle file attachment."""
        filetypes = [
            ("All supported files", "*.txt *.pdf *.docx *.doc *.jpg *.jpeg *.png"),
            ("Text files", "*.txt"),
            ("PDF files", "*.pdf"),
            ("Word documents", "*.docx *.doc"),
            ("Image files", "*.jpg *.jpeg *.png"),
            ("All files", "*.*")
        ]
        
        file_path = filedialog.askopenfilename(
            title="Select a file",
            filetypes=filetypes
        )
        
        if file_path:
            # Add file message
            file_message = Message(
                id=str(uuid.uuid4()),
                role='user',
                content=f"📎 Attached file: {Path(file_path).name}",
                timestamp=datetime.datetime.now().isoformat()
            )
            
            self.current_conversation.messages.append(file_message)
            self.add_message_widget(file_message)
            
            # Process file with assistant
            if hasattr(self.parent, 'schedule_task'):
                self.parent.schedule_task(self.process_file(file_path))
    
    async def process_file(self, file_path: str):
        """Process an attached file."""
        try:
            response = await self.assistant.process_document(file_path)
            
            assistant_message = Message(
                id=str(uuid.uuid4()),
                role='assistant',
                content=str(response),
                timestamp=datetime.datetime.now().isoformat(),
                model=self.current_model
            )
            
            self.current_conversation.messages.append(assistant_message)
            self.after(0, lambda: self.add_message_widget(assistant_message))
            self.after(0, self.update_conversation_stats)
            self.after(0, self._scroll_to_bottom)
            self.after(0, self.save_conversations)
            
        except Exception as e:
            logger.error(f"Error processing file: {e}")
            error_message = Message(
                id=str(uuid.uuid4()),
                role='system',
                content=f"Error processing file: {str(e)}",
                timestamp=datetime.datetime.now().isoformat()
            )
            self.current_conversation.messages.append(error_message)
            self.after(0, lambda: self.add_message_widget(error_message))
    
    def on_assistant_changed(self, event):
        """Handle assistant selection change."""
        selected_name = self.assistant_var.get()
        
        # Find the assistant by name
        assistants = self.assistant_manager.get_all_assistants()
        for assistant in assistants:
            if assistant.name == selected_name:
                self.current_assistant_profile = assistant
                break
        
        # Update the description
        if self.current_assistant_profile:
            description = self.current_assistant_profile.description[:60] + ("..." if len(self.current_assistant_profile.description) > 60 else "")
            self.assistant_desc_label.config(text=description)
            
            # Update current conversation's assistant
            if self.current_conversation:
                self.current_conversation.assistant_id = self.current_assistant_profile.id
                self.save_conversations()
        
        logger.info(f"Assistant changed to: {selected_name}")
    
    def on_theme_changed(self, event):
        """Handle theme selection change."""
        selected_theme_name = self.theme_var.get()
        
        # Find the theme by display name
        themes = self.theme_manager.get_all_themes()
        for theme in themes:
            if theme.display_name == selected_theme_name:
                self.theme_manager.set_current_theme(theme.name)
                break
        
        # Refresh the entire UI with new theme
        self.setup_styles()
        self.refresh_ui()
        
        logger.info(f"Theme changed to: {selected_theme_name}")
    
    def on_model_changed(self, event):
        """Handle model selection change."""
        self.current_model = self.model_var.get()
        self.model_info_label.config(text=self.current_model)
        
        if self.current_conversation:
            self.current_conversation.model = self.current_model
            self.save_conversations()
    
    def update_conversation_stats(self):
        """Update conversation statistics."""
        if not self.current_conversation:
            return
        
        message_count = len(self.current_conversation.messages)
        token_count = sum(len(msg.content.split()) for msg in self.current_conversation.messages)
        
        stats_text = f"Messages: {message_count}\nTokens: ~{token_count}"
        self.stats_label.config(text=stats_text)
    
    def _scroll_to_bottom(self):
        """Scroll messages to bottom."""
        self.messages_canvas.update_idletasks()
        self.messages_canvas.yview_moveto(1.0)
    
    def _on_mousewheel(self, event):
        """Handle mouse wheel scrolling."""
        self.messages_canvas.yview_scroll(int(-1 * (event.delta / 120)), "units")
    
    def load_conversations(self):
        """Load conversations from file."""
        try:
            conversations_file = Path("data/conversations.json")
            if conversations_file.exists():
                with open(conversations_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.conversations = [Conversation.from_dict(conv) for conv in data]
                logger.info(f"Loaded {len(self.conversations)} conversations")
            else:
                self.conversations = []
                logger.info("No conversations file found, starting fresh")
        except Exception as e:
            logger.error(f"Error loading conversations: {e}")
            self.conversations = []
    
    def save_conversations(self):
        """Save conversations to file."""
        try:
            # Ensure data directory exists
            data_dir = Path("data")
            data_dir.mkdir(exist_ok=True)
            
            # Save conversations
            conversations_file = data_dir / "conversations.json"
            with open(conversations_file, 'w', encoding='utf-8') as f:
                data = [conv.to_dict() for conv in self.conversations]
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            logger.debug(f"Saved {len(self.conversations)} conversations")
        except Exception as e:
            logger.error(f"Error saving conversations: {e}")
    
    def refresh_ui(self):
        """Refresh the entire UI with current theme."""
        # Destroy and recreate the main frame
        self.main_frame.destroy()
        
        # Recreate the UI
        self.setup_ui()
        
        # Refresh current conversation display
        if self.current_conversation:
            self.refresh_chat_display()
