"""
Graphical user interface entry point for the VoiceFlow AI Assistant.
"""
import asyncio
import sys
import logging
import signal
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.assistant import VoiceAssistant
from src.ui.main_window import MainWindow
from src.utils.config import load_config, find_config_file
from src.utils.logger import setup_logging

# Configure logging at the module level
logger = logging.getLogger(__name__)

def main():
    """Main entry point for the GUI application."""
    try:
        logger.info("Starting VoiceFlow AI Assistant GUI...")
        
        # Load configuration from the clean config file
        config_path = Path(__file__).parent.parent / 'clean_config.ini'
        if not config_path.exists():
            raise FileNotFoundError(f"Configuration file not found: {config_path}")
        config = load_config(config_path)
        
        # Set up logging
        log_level = config.get('DEFAULT', 'log_level', fallback='INFO')
        log_file = Path('logs') / 'voiceflow_ai_gui.log'
        setup_logging(log_level=log_level, log_file=str(log_file))
        
        logger.info("Initializing VoiceFlow AI Assistant...")
        
        # Create event loop for asyncio operations
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        # Initialize the assistant
        async def init_assistant():
            assistant = VoiceAssistant(config)
            await assistant.initialize()
            return assistant
        
        assistant = loop.run_until_complete(init_assistant())
        logger.info("Assistant initialized successfully")
        
        logger.info("Creating main window...")
        
        # Create the main window
        app = MainWindow(config, assistant)
        
        # Start the asyncio event loop in a background thread
        import threading
        def run_asyncio_loop():
            """Run the asyncio event loop in a background thread."""
            logger.debug("Starting asyncio event loop in background thread")
            try:
                loop.run_forever()
            except Exception as e:
                logger.error(f"Error in asyncio event loop: {e}", exc_info=True)
        
        # Start the background thread for asyncio
        asyncio_thread = threading.Thread(target=run_asyncio_loop, daemon=True)
        asyncio_thread.start()
        logger.debug("Asyncio event loop started in background thread")
        
        # Override the schedule_task method to use our loop
        def schedule_task_with_loop(coro):
            """Schedule a task in our asyncio loop."""
            logger.debug(f"Scheduling task: {coro.__qualname__}")
            
            def run_task():
                task = asyncio.run_coroutine_threadsafe(coro, loop)
                app.tasks.append(task)
                
                def task_done(future):
                    try:
                        future.result()
                        logger.debug("Task completed successfully")
                    except Exception as e:
                        logger.error(f"Task failed: {e}", exc_info=True)
                    finally:
                        if future in app.tasks:
                            app.tasks.remove(future)
                
                task.add_done_callback(task_done)
            
            # Schedule the task to run in the next Tkinter idle cycle
            app.after_idle(run_task)
        
        # Replace the schedule_task method
        app.schedule_task = schedule_task_with_loop
        
        # Set up signal handlers for clean shutdown
        def handle_sigint(signum, frame):
            logger.info("SIGINT received, shutting down...")
            # Stop the asyncio loop
            loop.call_soon_threadsafe(loop.stop)
            app.on_close()
            
        signal.signal(signal.SIGINT, handle_sigint)
        signal.signal(signal.SIGTERM, handle_sigint)
        
        logger.info("Starting main event loop...")
        
        try:
            # Run the Tkinter main loop
            app.mainloop()
        except KeyboardInterrupt:
            logger.info("Received keyboard interrupt, shutting down...")
        finally:
            # Clean shutdown
            logger.info("Performing final cleanup...")
            
            # Stop the asyncio loop
            if loop.is_running():
                loop.call_soon_threadsafe(loop.stop)
            
            # Wait a bit for the loop to stop
            import time
            time.sleep(0.1)
            
            # Close the loop
            if not loop.is_closed():
                loop.close()
                
            logger.info("GUI shutdown complete")
        
    except Exception as e:
        logger.error(f"Error in main: {e}", exc_info=True)
        raise

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        logger.info("Application terminated by user.")
    except Exception as e:
        logger.error(f"Unhandled exception: {e}", exc_info=True)
        sys.exit(1)
