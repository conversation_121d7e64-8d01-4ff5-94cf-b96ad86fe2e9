{"filesystem": {"name": "filesystem", "display_name": "File System Tools", "description": "Tools for file and directory operations", "transport": "stdio", "enabled": false, "auto_start": false, "command": ["npx", "-y", "@modelcontextprotocol/server-filesystem"], "args": ["/tmp"]}, "git": {"name": "git", "display_name": "<PERSON><PERSON>", "description": "Git repository management tools", "transport": "stdio", "enabled": false, "auto_start": false, "command": ["npx", "-y", "@modelcontextprotocol/server-git"], "args": ["--repository", "."]}, "github": {"name": "github", "display_name": "GitHub Tools", "description": "GitHub API integration tools", "transport": "stdio", "enabled": false, "auto_start": false, "command": ["npx", "-y", "@modelcontextprotocol/server-github"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "your_github_token_here"}}, "brave_search": {"name": "brave_search", "display_name": "Brave Search", "description": "Web search using Brave Search API", "transport": "stdio", "enabled": false, "auto_start": false, "command": ["npx", "-y", "@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_API_KEY": "your_brave_api_key_here"}}}