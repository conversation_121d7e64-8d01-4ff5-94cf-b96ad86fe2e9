"""
VoiceFlow AI - A Python-based, voice-enabled AI assistant with multi-LLM support.

This package contains all the core functionality for the VoiceFlow AI assistant,
including voice processing, LLM integrations, and the user interface.
"""

__version__ = "0.1.0"

import logging
from pathlib import Path

# Configure package-level logger
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Define package paths
PACKAGE_DIR = Path(__file__).parent
ROOT_DIR = PACKAGE_DIR.parent
CONFIG_DIR = ROOT_DIR
DATA_DIR = ROOT_DIR / "data"
LOGS_DIR = ROOT_DIR / "logs"

# Create necessary directories
for directory in [DATA_DIR, LOGS_DIR]:
    directory.mkdir(exist_ok=True)
