"""
Assistant manager for handling different AI personas and configurations.
"""
import json
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
import uuid

logger = logging.getLogger(__name__)

@dataclass
class AssistantProfile:
    """Represents an AI assistant profile/persona."""
    id: str
    name: str
    description: str
    system_prompt: str
    model: str
    temperature: float = 0.7
    max_tokens: int = 2000
    avatar: Optional[str] = None
    color: str = "#007acc"
    created_at: str = ""
    updated_at: str = ""
    is_builtin: bool = False
    tags: List[str] = None
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = []
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AssistantProfile':
        return cls(**data)

class AssistantManager:
    """Manages AI assistant profiles and configurations."""
    
    def __init__(self, data_dir: str = "data"):
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(exist_ok=True)
        self.assistants_file = self.data_dir / "assistants.json"
        
        self.assistants: Dict[str, AssistantProfile] = {}
        self.load_assistants()
        
        # Create default assistants if none exist
        if not self.assistants:
            self.create_default_assistants()
    
    def load_assistants(self):
        """Load assistants from file."""
        try:
            if self.assistants_file.exists():
                with open(self.assistants_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.assistants = {
                        aid: AssistantProfile.from_dict(profile_data)
                        for aid, profile_data in data.items()
                    }
                logger.info(f"Loaded {len(self.assistants)} assistant profiles")
            else:
                self.assistants = {}
                logger.info("No assistants file found, starting fresh")
        except Exception as e:
            logger.error(f"Error loading assistants: {e}")
            self.assistants = {}
    
    def save_assistants(self):
        """Save assistants to file."""
        try:
            with open(self.assistants_file, 'w', encoding='utf-8') as f:
                data = {
                    aid: profile.to_dict()
                    for aid, profile in self.assistants.items()
                }
                json.dump(data, f, indent=2, ensure_ascii=False)
            logger.debug(f"Saved {len(self.assistants)} assistant profiles")
        except Exception as e:
            logger.error(f"Error saving assistants: {e}")
    
    def create_default_assistants(self):
        """Create default assistant profiles."""
        import datetime
        timestamp = datetime.datetime.now().isoformat()
        
        default_assistants = [
            {
                "name": "General Assistant",
                "description": "A helpful, harmless, and honest AI assistant",
                "system_prompt": "You are a helpful, harmless, and honest AI assistant. You provide accurate information and assist users with a wide variety of tasks. Be concise but thorough in your responses.",
                "model": "gpt-3.5-turbo",
                "temperature": 0.7,
                "max_tokens": 2000,
                "color": "#007acc",
                "tags": ["general", "helpful"],
                "is_builtin": True
            },
            {
                "name": "Code Assistant",
                "description": "Expert programming assistant for code review, debugging, and development",
                "system_prompt": "You are an expert programming assistant. You help with code review, debugging, optimization, and explaining programming concepts. You write clean, efficient, and well-documented code. Always consider best practices and security implications.",
                "model": "gpt-4",
                "temperature": 0.3,
                "max_tokens": 3000,
                "color": "#28a745",
                "tags": ["programming", "code", "development"],
                "is_builtin": True
            },
            {
                "name": "Creative Writer",
                "description": "Creative writing assistant for stories, poems, and creative content",
                "system_prompt": "You are a creative writing assistant. You help with storytelling, poetry, creative writing, and content creation. You have a vivid imagination and can adapt to different writing styles and genres. Be creative, engaging, and inspiring.",
                "model": "gpt-4",
                "temperature": 0.9,
                "max_tokens": 2500,
                "color": "#dc3545",
                "tags": ["creative", "writing", "storytelling"],
                "is_builtin": True
            },
            {
                "name": "Research Assistant",
                "description": "Academic and research assistant for analysis and information gathering",
                "system_prompt": "You are a research assistant specializing in academic and professional research. You help with information gathering, analysis, citation formatting, and research methodology. You provide well-sourced, accurate, and comprehensive information.",
                "model": "gpt-4",
                "temperature": 0.4,
                "max_tokens": 3000,
                "color": "#6f42c1",
                "tags": ["research", "academic", "analysis"],
                "is_builtin": True
            },
            {
                "name": "Language Tutor",
                "description": "Language learning assistant for grammar, vocabulary, and conversation practice",
                "system_prompt": "You are a language learning tutor. You help with grammar, vocabulary, pronunciation, and conversation practice. You can explain language concepts clearly and provide examples. Be patient, encouraging, and adapt to the learner's level.",
                "model": "gpt-3.5-turbo",
                "temperature": 0.6,
                "max_tokens": 2000,
                "color": "#fd7e14",
                "tags": ["language", "education", "tutor"],
                "is_builtin": True
            },
            {
                "name": "Business Advisor",
                "description": "Business and entrepreneurship advisor for strategy and planning",
                "system_prompt": "You are a business advisor with expertise in entrepreneurship, strategy, marketing, and business operations. You provide practical advice for business planning, market analysis, and growth strategies. Be professional and data-driven in your recommendations.",
                "model": "gpt-4",
                "temperature": 0.5,
                "max_tokens": 2500,
                "color": "#20c997",
                "tags": ["business", "strategy", "entrepreneurship"],
                "is_builtin": True
            }
        ]
        
        for assistant_data in default_assistants:
            assistant_id = str(uuid.uuid4())
            assistant = AssistantProfile(
                id=assistant_id,
                created_at=timestamp,
                updated_at=timestamp,
                **assistant_data
            )
            self.assistants[assistant_id] = assistant
        
        self.save_assistants()
        logger.info(f"Created {len(default_assistants)} default assistant profiles")
    
    def get_assistant(self, assistant_id: str) -> Optional[AssistantProfile]:
        """Get an assistant profile by ID."""
        return self.assistants.get(assistant_id)
    
    def get_all_assistants(self) -> List[AssistantProfile]:
        """Get all assistant profiles."""
        return list(self.assistants.values())
    
    def get_assistants_by_tag(self, tag: str) -> List[AssistantProfile]:
        """Get assistants filtered by tag."""
        return [
            assistant for assistant in self.assistants.values()
            if tag in assistant.tags
        ]
    
    def create_assistant(self, name: str, description: str, system_prompt: str, 
                        model: str, **kwargs) -> AssistantProfile:
        """Create a new assistant profile."""
        import datetime
        
        assistant_id = str(uuid.uuid4())
        timestamp = datetime.datetime.now().isoformat()
        
        assistant = AssistantProfile(
            id=assistant_id,
            name=name,
            description=description,
            system_prompt=system_prompt,
            model=model,
            created_at=timestamp,
            updated_at=timestamp,
            **kwargs
        )
        
        self.assistants[assistant_id] = assistant
        self.save_assistants()
        
        logger.info(f"Created new assistant: {name} ({assistant_id})")
        return assistant
    
    def update_assistant(self, assistant_id: str, **updates) -> bool:
        """Update an assistant profile."""
        if assistant_id not in self.assistants:
            return False
        
        import datetime
        assistant = self.assistants[assistant_id]
        
        # Don't allow updating builtin assistants' core properties
        if assistant.is_builtin:
            allowed_updates = {'temperature', 'max_tokens', 'model'}
            updates = {k: v for k, v in updates.items() if k in allowed_updates}
        
        # Update fields
        for key, value in updates.items():
            if hasattr(assistant, key):
                setattr(assistant, key, value)
        
        assistant.updated_at = datetime.datetime.now().isoformat()
        self.save_assistants()
        
        logger.info(f"Updated assistant: {assistant.name} ({assistant_id})")
        return True
    
    def delete_assistant(self, assistant_id: str) -> bool:
        """Delete an assistant profile."""
        if assistant_id not in self.assistants:
            return False
        
        assistant = self.assistants[assistant_id]
        
        # Don't allow deleting builtin assistants
        if assistant.is_builtin:
            logger.warning(f"Cannot delete builtin assistant: {assistant.name}")
            return False
        
        del self.assistants[assistant_id]
        self.save_assistants()
        
        logger.info(f"Deleted assistant: {assistant.name} ({assistant_id})")
        return True
    
    def duplicate_assistant(self, assistant_id: str, new_name: str = None) -> Optional[AssistantProfile]:
        """Duplicate an existing assistant profile."""
        if assistant_id not in self.assistants:
            return None
        
        original = self.assistants[assistant_id]
        
        # Create new assistant with copied properties
        new_assistant = self.create_assistant(
            name=new_name or f"{original.name} (Copy)",
            description=original.description,
            system_prompt=original.system_prompt,
            model=original.model,
            temperature=original.temperature,
            max_tokens=original.max_tokens,
            avatar=original.avatar,
            color=original.color,
            tags=original.tags.copy(),
            is_builtin=False  # Copies are never builtin
        )
        
        logger.info(f"Duplicated assistant: {original.name} -> {new_assistant.name}")
        return new_assistant
    
    def search_assistants(self, query: str) -> List[AssistantProfile]:
        """Search assistants by name, description, or tags."""
        query = query.lower()
        results = []
        
        for assistant in self.assistants.values():
            if (query in assistant.name.lower() or
                query in assistant.description.lower() or
                any(query in tag.lower() for tag in assistant.tags)):
                results.append(assistant)
        
        return results
    
    def get_default_assistant(self) -> Optional[AssistantProfile]:
        """Get the default assistant (first general assistant)."""
        general_assistants = self.get_assistants_by_tag("general")
        return general_assistants[0] if general_assistants else None
    
    def export_assistant(self, assistant_id: str) -> Optional[Dict[str, Any]]:
        """Export an assistant profile to a dictionary."""
        assistant = self.get_assistant(assistant_id)
        if not assistant:
            return None
        
        return assistant.to_dict()
    
    def import_assistant(self, data: Dict[str, Any]) -> Optional[AssistantProfile]:
        """Import an assistant profile from a dictionary."""
        try:
            # Generate new ID and timestamps
            import datetime
            data['id'] = str(uuid.uuid4())
            data['created_at'] = datetime.datetime.now().isoformat()
            data['updated_at'] = data['created_at']
            data['is_builtin'] = False  # Imported assistants are never builtin
            
            assistant = AssistantProfile.from_dict(data)
            self.assistants[assistant.id] = assistant
            self.save_assistants()
            
            logger.info(f"Imported assistant: {assistant.name} ({assistant.id})")
            return assistant
        except Exception as e:
            logger.error(f"Error importing assistant: {e}")
            return None
