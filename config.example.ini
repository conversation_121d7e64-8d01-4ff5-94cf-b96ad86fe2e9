[DEFAULT]
# General Settings
app_name = VoiceFlow AI
version = 0.1.0
log_level = INFO

[VOICE]
# Speech-to-Text Settings
wake_word = hey assistant
energy_threshold = 4000  # Adjust based on your microphone
pause_threshold = 0.8    # Seconds of silence to end voice input

# Text-to-Speech Settings
tts_rate = 150           # Words per minute
tts_volume = 0.9         # 0.0 to 1.0
tts_voice = 0            # 0 for system default

[OPENAI]
api_key = your_openai_api_key
model = gpt-4-turbo-preview
max_tokens = 1000
temperature = 0.7

[ANTHROPIC]
api_key = your_anthropic_api_key
model = claude-3-opus-20240229
max_tokens = 1000
temperature = 0.7

[GEMINI]
api_key = your_gemini_api_key
model = gemini-pro
max_tokens = 1000
temperature = 0.7

[OLLAMA]
base_url = http://localhost:11434
model = deepseek-r1:8b  # or any model you have installed

[UI]
theme = darkly  # Options: cosmo, flatly, journal, darkly, etc.
width = 800
height = 600

[FILES]
# Maximum file size in MB
max_file_size = 10
# Allowed file extensions
allowed_extensions = .txt,.pdf,.docx,.jpg,.jpeg,.png
