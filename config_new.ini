[DEFAULT]
app_name = Cherry Studio Clone
version = 1.0.0
log_level = INFO

[LLM]
# Default provider to use (openai, anthropic, gemini, ollama, lmstudio, vllm, cohere, mistral, perplexity, groq)
provider = ollama
model = llama3.1:8b
temperature = 0.7
max_tokens = 2000
stream = false
timeout = 30.0

[OPENAI]
api_key = your_openai_api_key_here
base_url = https://api.openai.com/v1
model = gpt-4o-mini
temperature = 0.7
max_tokens = 2000
stream = false

[ANTHROPIC]
api_key = your_anthropic_api_key_here
base_url = https://api.anthropic.com
model = claude-3-5-sonnet-20241022
temperature = 0.7
max_tokens = 2000
stream = false

[GEMINI]
api_key = your_gemini_api_key_here
model = gemini-1.5-flash
temperature = 0.7
max_tokens = 2000
stream = false

[OLLAMA]
base_url = http://localhost:11434
model = llama3.1:8b
temperature = 0.7
max_tokens = 2000
stream = false

[LMSTUDIO]
base_url = http://localhost:1234/v1
api_key = lm-studio
model = unsloth/gemma-3-12b-it
temperature = 0.7
max_tokens = 2000
stream = false

[VLLM]
base_url = http://localhost:8000
api_key = 
model = meta-llama/Llama-3.1-8B-Instruct
temperature = 0.7
max_tokens = 2000
stream = false

[COHERE]
api_key = your_cohere_api_key_here
base_url = https://api.cohere.ai/v1
model = command-r-plus
temperature = 0.7
max_tokens = 2000
stream = false

[MISTRAL]
api_key = your_mistral_api_key_here
base_url = https://api.mistral.ai/v1
model = mistral-large-latest
temperature = 0.7
max_tokens = 2000
stream = false

[PERPLEXITY]
api_key = your_perplexity_api_key_here
base_url = https://api.perplexity.ai
model = llama-3.1-sonar-large-128k-online
temperature = 0.7
max_tokens = 2000
stream = false

[GROQ]
api_key = your_groq_api_key_here
base_url = https://api.groq.com/openai/v1
model = llama-3.1-70b-versatile
temperature = 0.7
max_tokens = 2000
stream = false

[VOICE]
enable_voice = false
wake_word = yo
language = en-US
energy_threshold = 4000
pause_threshold = 0.8
dynamic_energy_threshold = true
tts_rate = 150
tts_volume = 0.9
tts_voice = 

[UI]
theme = company_theme
width = 1200
height = 800
min_width = 800
min_height = 600

[LOGGING]
log_level = INFO
log_file = logs/cherry_studio_clone.log
max_log_size = 10
backup_count = 5
