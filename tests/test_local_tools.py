import os
import sqlite3
import subprocess
import sys
import tempfile
from pathlib import Path

import pytest

from src.mcp.local_tools import LocalToolsServer
from src.mcp.types import MCPToolResult

# Initialize the local tools server instance
tools_server = LocalToolsServer()

def test_calculate_success():
    # Test a simple calculation: 2 + 2 * 3 = 8
    result: MCPToolResult = pytest.run(asyncio_run=tools_server.call_tool)("calculate", {"expression": "2 + 2 * 3"})
    # Alternatively, use asyncio.run for synchronous test
    import asyncio
    result = asyncio.run(tools_server.call_tool("calculate", {"expression": "2 + 2 * 3"}))
    assert "Result: 8" in result.content[0]["text"]

def test_read_and_write_file(tmp_path: Path):
    # Create a temporary file and use write_file and read_file tools.
    file_path = tmp_path / "test.txt"
    test_content = "Hello, Augie!"
    import asyncio
    # Write file
    write_result = asyncio.run(tools_server.call_tool("write_file", {"path": str(file_path), "content": test_content}))
    assert "written to" in write_result.content[0]["text"]
    # Read file
    read_result = asyncio.run(tools_server.call_tool("read_file", {"path": str(file_path)}))
    assert test_content in read_result.content[0]["text"]

def test_sqlite_query(tmp_path: Path):
    # Create a temporary SQLite database and table, insert data, then test the sqlite_query tool.
    db_file = tmp_path / "test.db"
    conn = sqlite3.connect(str(db_file))
    cur = conn.cursor()
    cur.execute("CREATE TABLE test (id INTEGER PRIMARY KEY, name TEXT)")
    cur.execute("INSERT INTO test (name) VALUES ('Alice')")
    cur.execute("INSERT INTO test (name) VALUES ('Bob')")
    conn.commit()
    conn.close()

    import asyncio
    result = asyncio.run(tools_server.call_tool("sqlite_query", {
        "db_path": str(db_file),
        "query": "SELECT * FROM test",
        "max_rows": 10
    }))
    text = result.content[0]["text"]
    assert "Alice" in text and "Bob" in text

def test_unknown_tool():
    # Test calling a tool that doesn't exist.
    import asyncio
    result = asyncio.run(tools_server.call_tool("non_existent_tool", {}))
    assert result.is_error
    assert "Unknown tool" in result.content[0]["text"]

# Additional tests for image_info and lint_python can be added
# if Pillow and flake8 are available in the test environment.
if __name__ == "__main__":
    pytest.main()
