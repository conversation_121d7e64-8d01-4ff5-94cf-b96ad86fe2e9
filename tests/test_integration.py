"""
Integration tests for the VoiceFlow AI Assistant.

These tests verify that all components work together correctly.
"""
import asyncio
import os
import sys
import unittest
from pathlib import Path
from unittest.mock import patch, MagicMock, AsyncMock

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.assistant import VoiceAssistant
from src.utils.config import load_config

class TestVoiceFlowAIIntegration(unittest.IsolatedAsyncioTestCase):
    """Integration tests for the VoiceFlow AI Assistant."""
    
    async def asyncSetUp(self):
        """Set up test fixtures."""
        # Load test configuration from test file
        test_config_path = Path(__file__).parent / 'test_config.ini'
        self.config = load_config(test_config_path)
        
        # Create test directories
        self.test_dir = Path('test_data')
        self.test_dir.mkdir(exist_ok=True)
        
        # Create a test text file
        self.test_text_file = self.test_dir / 'test.txt'
        with open(self.test_text_file, 'w') as f:
            f.write("This is a test document.\nIt contains multiple lines of text.")
    
    async def asyncTearDown(self):
        """Clean up test fixtures."""
        # Clean up test files
        if self.test_text_file.exists():
            self.test_text_file.unlink()
        
        if self.test_dir.exists():
            try:
                self.test_dir.rmdir()
            except OSError:
                pass  # Directory not empty
    
    @patch('src.voice.stt.SpeechToText.listen_for_wake_word')
    @patch('src.voice.stt.SpeechToText.listen')
    @patch('src.voice.tts.TextToSpeech.speak')
    @patch('src.llm.openai.AsyncOpenAI')
    async def test_voice_interaction(self, mock_openai, mock_tts_speak, mock_stt_listen, mock_wake_word):
        """Test a complete voice interaction."""
        # Set up mocks
        mock_wake_word.return_value = True
        mock_stt_listen.return_value = "What's the weather like today?"
        mock_tts_speak.return_value = None
        
        # Create a mock LLMResponse object
        from src.llm.base import LLMResponse
        mock_llm_response = LLMResponse(
            content="The weather is sunny today!",
            model="gpt-3.5-turbo",
            prompt_tokens=10,
            completion_tokens=5,
            total_tokens=15,
            finish_reason="stop"
        )
        
        # Initialize the assistant
        assistant = VoiceAssistant(self.config)
        await assistant.initialize()
        
        # Mock the generate_response method to return our mock LLMResponse
        with patch.object(assistant.current_llm, 'generate_response', new_callable=AsyncMock) as mock_generate:
            mock_generate.return_value = mock_llm_response
            
            # Test voice interaction
            response = await assistant.process_voice_input()
            
            # Verify the response is a string and contains expected content
            self.assertIsInstance(response, str)
            self.assertIn("sunny", response.lower())
            
            # Verify the response matches the mocked LLM response content
            self.assertEqual(response, mock_llm_response.content)
            
            # Verify the mocks were called
            mock_wake_word.assert_awaited_once()
            mock_stt_listen.assert_awaited_once()
            mock_tts_speak.assert_awaited_once()
            mock_generate.assert_awaited_once()
    
    @patch('src.llm.openai.AsyncOpenAI')
    async def test_text_interaction(self, mock_openai):
        """Test a text-based interaction."""
        # Mock OpenAI client
        mock_client = AsyncMock()
        mock_openai.return_value = mock_client
        
        # Create a mock LLMResponse object
        from src.llm.base import LLMResponse
        mock_llm_response = LLMResponse(
            content="42",
            model="gpt-3.5-turbo",
            prompt_tokens=8,
            completion_tokens=1,
            total_tokens=9,
            finish_reason="stop"
        )
        
        # Initialize the assistant
        assistant = VoiceAssistant(self.config)
        await assistant.initialize()
        
        # Mock the generate_response method to return our mock LLMResponse
        with patch.object(assistant.current_llm, 'generate_response', new_callable=AsyncMock) as mock_generate:
            mock_generate.return_value = mock_llm_response
            
            # Test text interaction
            response = await assistant.process_text_input(
                "What is the answer to life, the universe, and everything?"
            )
            
            # Verify the response is the content from LLMResponse
            # The process_text_input method should return the content directly
            self.assertIsInstance(response, str)
            self.assertEqual(response, "42")
            
            # Verify the LLM's generate_response was called with the correct arguments
            mock_generate.assert_awaited_once()
            
            # Verify the OpenAI client was not called directly (since we're mocking generate_response)
            mock_client.chat.completions.create.assert_not_called()
    
    @patch('src.llm.openai.AsyncOpenAI')
    async def test_document_processing(self, mock_openai):
        """Test document processing functionality."""
        # Create a mock LLMResponse object for document processing
        from src.llm.base import LLMResponse
        mock_llm_response = LLMResponse(
            content="This is a summary of the test document.",
            model="gpt-3.5-turbo",
            prompt_tokens=20,
            completion_tokens=10,
            total_tokens=30,
            finish_reason="stop"
        )
        
        # Initialize the assistant
        assistant = VoiceAssistant(self.config)
        await assistant.initialize()
        
        # Mock the file operations and LLM response
        with patch('builtins.open', new_callable=unittest.mock.mock_open, read_data='Test document content') as mock_file, \
             patch.object(assistant, 'process_text_input', new_callable=AsyncMock) as mock_process_text:
            
            # Set up mocks
            mock_process_text.return_value = mock_llm_response.content
            
            # Test document processing
            response = await assistant.process_document(str(self.test_text_file))
            
            # Verify the response is a string and contains the expected content
            self.assertIsInstance(response, str)
            self.assertIn("summary", response.lower())
            
            # Verify the response matches the mocked LLM response content
            self.assertEqual(response, mock_llm_response.content)
            
            # Verify the file was opened and read
            mock_file.assert_called_once_with(str(self.test_text_file), 'r', encoding='utf-8')
            
            self.assertIsInstance(response, str)
            self.assertEqual(response, "42")
            
            # Verify the LLM's generate_response was called with the correct arguments
            mock_generate.assert_awaited_once()
            
            # Verify the OpenAI client was not called directly (since we're mocking generate_response)
            mock_client.chat.completions.create.assert_not_called()
    
    @patch('src.llm.openai.AsyncOpenAI')
    @patch('src.utils.document_processor.process_document')
    async def test_document_processing(self, mock_process_doc, mock_openai):
        """Test document processing functionality."""
        # Create a mock LLMResponse object for document processing
        from src.llm.base import LLMResponse
        mock_llm_response = LLMResponse(
            content="This is a summary of the test document.",
            model="gpt-3.5-turbo",
            prompt_tokens=20,
            completion_tokens=10,
            total_tokens=30,
            finish_reason="stop"
        )
        
        # Set up the document processor mock
        test_doc_content = "Test document content"
        mock_process_doc.return_value = test_doc_content
        
        # Initialize the assistant
        assistant = VoiceAssistant(self.config)
        await assistant.initialize()
        
        # Mock the process_text_input method
        with patch.object(assistant, 'process_text_input', new_callable=AsyncMock) as mock_process_text:
            # Set up the mock to return our test response
            mock_process_text.return_value = mock_llm_response.content
            
            # Test document processing
            response = await assistant.process_document(str(self.test_text_file))
            
            # Verify the response is a string and contains the expected content
            self.assertIsInstance(response, str)
            self.assertIn("summary", response.lower())
            
            # Verify the response matches the mocked LLM response content
            self.assertEqual(response, mock_llm_response.content)
            
            # Verify the document processor was called with the correct file path
            mock_process_doc.assert_called_once_with(str(self.test_text_file))
            
            # Verify process_text_input was called with the expected prompt
            self.assertTrue(mock_process_text.called)
            call_args = mock_process_text.call_args[0][0]
            self.assertIn("summarize the following document", call_args.lower())
            self.assertIn(test_doc_content, call_args)
    
    @patch('src.llm.openai.OpenAIClient')
    @patch('src.llm.anthropic.AnthropicClient')
    async def test_llm_provider_switching(self, mock_anth_client_cls, mock_openai_client_cls):
        """Test switching between different LLM providers."""
        # Create mock LLMResponse objects for each provider
        from src.llm.base import LLMResponse
        
        # Set up mock responses
        openai_response = LLMResponse(
            content="OpenAI response",
            model="gpt-3.5-turbo",
            prompt_tokens=5,
            completion_tokens=2,
            total_tokens=7,
            finish_reason="stop"
        )
        
        anthropic_response = LLMResponse(
            content="Anthropic response",
            model="claude-3-opus-20240229",
            prompt_tokens=6,
            completion_tokens=3,
            total_tokens=9,
            finish_reason="end_turn"
        )
        
        # Set up mock OpenAI client
        mock_openai_instance = AsyncMock()
        mock_openai_instance.generate_response = AsyncMock(return_value=openai_response)
        mock_openai_client_cls.return_value = mock_openai_instance
        
        # Set up mock Anthropic client
        mock_anth_instance = AsyncMock()
        mock_anth_instance.generate_response = AsyncMock(return_value=anthropic_response)
        mock_anth_client_cls.return_value = mock_anth_instance
        
        # Initialize the assistant with the test config
        assistant = VoiceAssistant(self.config)
        await assistant.initialize()
        
        # Test OpenAI provider
        result = await assistant.set_llm_provider('openai')
        self.assertTrue(result, "Failed to switch to OpenAI provider")
        
        # Test processing with OpenAI
        response = await assistant.process_text_input("Test OpenAI")
        
        # Verify the response is as expected
        self.assertEqual(response, str(openai_response.content))
        
        # Verify generate_response was called with the correct arguments
        mock_openai_instance.generate_response.assert_awaited_once()
        
        # Get the messages passed to generate_response
        call_args = mock_openai_instance.generate_response.call_args[1]
        self.assertIn('messages', call_args)
        
        # Clear the mock for the next test
        mock_openai_instance.generate_response.reset_mock()
        
        # Test switching to Anthropic
        result = await assistant.set_llm_provider('anthropic')
        self.assertTrue(result, "Failed to switch to Anthropic provider")
        
        # Test processing with Anthropic
        response = await assistant.process_text_input("Test Anthropic")
        
        # Verify the response is as expected
        self.assertEqual(response, str(anthropic_response.content))
        
        # Verify generate_response was called with the correct arguments
        mock_anth_instance.generate_response.assert_awaited_once()
        
        # Get the messages passed to generate_response
        call_args = mock_anth_instance.generate_response.call_args[1]
        self.assertIn('messages', call_args)
    
    @patch('src.voice.stt.SpeechToText.listen_for_wake_word')
    @patch('src.llm.openai.AsyncOpenAI')
    @patch('src.voice.stt.SpeechToText.listen')
    @patch('src.voice.tts.TextToSpeech.speak')
    async def test_error_handling(self, mock_tts_speak, mock_stt_listen, mock_openai, mock_wake_word):
        """Test error handling and recovery."""
        # Initialize the assistant
        assistant = VoiceAssistant(self.config)
        await assistant.initialize()
        
        # Test 1: LLM processing error in process_text_input
        with patch.object(assistant.current_llm, 'generate_response', 
                        side_effect=Exception("LLM error")) as mock_generate:
            response = await assistant.process_text_input("Test error")
            self.assertIsInstance(response, str)
            self.assertIn("error", response.lower())
            mock_generate.assert_awaited_once()
        
        # Test 2: File not found error in process_document
        from src.utils.document_processor import process_document as original_process_document
        
        def mock_process_document(file_path, **kwargs):
            if str(file_path) == "nonexistent.txt":
                raise FileNotFoundError("File not found")
            return original_process_document(file_path, **kwargs)
        
        with patch('src.utils.document_processor.process_document', side_effect=mock_process_document) as mock_doc_processor:
            response = await assistant.process_document("nonexistent.txt")
            self.assertIsInstance(response, str)
            self.assertIn("couldn't process", response.lower())
            # Verify the document processor was called
            mock_doc_processor.assert_called_once()
        
        # Test 3: Wake word detection error
        mock_wake_word.side_effect = Exception("Wake word error")
        response = await assistant.process_voice_input()
        self.assertIsInstance(response, str)
        self.assertIn("error", response.lower())
        mock_wake_word.assert_awaited_once()
        
        # Reset mocks for next test
        mock_wake_word.side_effect = None
        mock_wake_word.reset_mock()
        mock_wake_word.return_value = True
        
        # Test 4: Speech recognition error
        mock_stt_listen.side_effect = Exception("Speech recognition error")
        
        response = await assistant.process_voice_input()
        self.assertIsInstance(response, str)
        self.assertIn("error", response.lower())
        
        # Reset speech recognition mock for next test
        mock_stt_listen.side_effect = None
        mock_stt_listen.return_value = "Test input"
        
        # Test 5: TTS error (should be caught and logged, not propagated)
        mock_tts_speak.side_effect = Exception("TTS error")
        
        with patch('logging.Logger.error') as mock_log_error:
            response = await assistant.process_voice_input()
            self.assertIsInstance(response, str)
            self.assertIn("error", response.lower())
            
            # Check that the TTS error was logged
            tts_error_logged = any(
                len(args) > 0 and "TTS error" in str(args[0])
                for args, _ in mock_log_error.call_args_list
            )
            self.assertTrue(tts_error_logged, "TTS error was not logged")

if __name__ == "__main__":
    unittest.main()
