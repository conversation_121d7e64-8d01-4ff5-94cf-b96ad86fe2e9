 import pytest
from src.translation import translate_text

def test_translate_text():
    # Test translation from English to Spanish
    result = translate_text("Hello, world!", src_lang="en", dest_lang="es")
    # Our stub function simulates translation by appending a note.
    assert result is not None
    assert "es translation" in result

def test_translate_empty():
    # Testing empty string translation from English to French
    result = translate_text("", src_lang="en", dest_lang="fr")
    assert result is not None
    assert "fr translation" in result

if __name__ == "__main__":
    pytest.main()
