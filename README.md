# 🍒 Augie - AI Assistant Platform

**A beautiful, powerful Python implementation of an AI Assistant with your company's signature pink, baby blue, and white branding.**

[![License: AGPL v3](https://img.shields.io/badge/License-AGPL%20v3-blue.svg)](https://www.gnu.org/licenses/agpl-3.0)
[![Python 3.8+](https://img.shields.io/badge/python-3.8+-blue.svg)](https://www.python.org/downloads/)

---

## 🌟 **What is Augie?**

Augie is a comprehensive AI assistant platform featuring:

- **🎨 Beautiful Company Branding** - Pink, baby blue, and white color scheme
- **🤖 10 LLM Providers** - OpenAI, Anthropic, Google, Ollama, vLLM, Cohere, Mistral, Perplexity, Groq, LM Studio
- **🛠️ MCP Integration** - Model Context Protocol with 12+ built-in tools
- **🎭 AI Personas** - 6 pre-configured AI assistants for different tasks
- **🎨 Theme System** - Multiple beautiful themes with real-time switching
- **💬 Modern Chat Interface** - 3-panel layout with conversation management
- **🔌 Extensible Architecture** - Plugin-ready for custom features

## 🚀 **Quick Start**

### Prerequisites
- Python 3.8 or higher
- pip package manager

### Installation

```bash
# Clone the repository
git clone https://github.com/wspotter/Augie.git
cd Augie

# Create virtual environment (recommended)
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Copy and configure settings
cp config.enhanced.ini config.ini
# Edit config.ini with your API keys
```

### Launch Augie

```bash
# Start the application
python test_modern_chat.py
```

Then click:
- **🚀 Modern Chat** - Beautiful Augie interface
- **🔌 Providers** - Monitor LLM provider health
- **🛠️ MCP Tools** - Manage tools and integrations

## 🎯 **Key Features**

### **🤖 AI Capabilities**
- **Multiple LLM Providers**: Support for all major AI providers
- **Smart Assistant Switching**: 6 specialized AI personas
- **Tool Integration**: 12+ built-in tools via MCP
- **Voice Interaction**: Speech-to-text and text-to-speech
- **Document Processing**: PDF, Word, images with OCR

### **🎨 Beautiful Interface**
- **Company Branding**: Pink (#FFB6C1), Baby Blue (#87CEEB), White (#ffffff)
- **4 Built-in Themes**: Light/dark variants with company colors
- **Modern 3-Panel Layout**: Conversations, chat, settings
- **Real-time Theme Switching**: Change appearance instantly

### **🛠️ MCP Tools (Local)**
- **File Operations**: Read, write, list directories
- **Web Requests**: HTTP requests, file downloads
- **System Utils**: Command execution, system info
- **Text Processing**: Search, encode/decode
- **Calculations**: Mathematical expressions
- **And more...**

## 📁 **Project Structure**

```
Augie/
├── src/
│   ├── assistants/          # AI persona management
│   ├── llm/                 # LLM provider integrations
│   ├── mcp/                 # Model Context Protocol
│   ├── ui/                  # User interface components
│   ├── utils/               # Utility functions
│   ├── voice/               # Voice processing
│   └── assistant.py         # Core assistant logic
├── data/                    # Application data
├── logs/                    # Log files
├── config.enhanced.ini      # Example configuration
├── requirements.txt         # Python dependencies
└── test_modern_chat.py      # Application launcher
```

## 🔧 **Configuration**

Edit `config.ini` to configure your AI providers:

```ini
[LLM]
provider = ollama  # or openai, anthropic, gemini, etc.

[OPENAI]
api_key = your_openai_api_key_here
model = gpt-4o-mini

[ANTHROPIC]
api_key = your_anthropic_api_key_here
model = claude-3-5-sonnet-20241022

[UI]
theme = company_theme  # Your beautiful company theme!
```

## 🎭 **Built-in AI Assistants**

1. **🤝 General Assistant** - Helpful for everyday tasks
2. **💻 Code Assistant** - Expert programming help
3. **✍️ Creative Writer** - Storytelling and creative content
4. **🔬 Research Assistant** - Academic research and analysis
5. **🌍 Language Tutor** - Language learning and practice
6. **💼 Business Advisor** - Business strategy and planning

## 🛠️ **MCP Tools**

Augie includes 12 powerful local tools:

| Tool | Description | Example Use |
|------|-------------|-------------|
| `read_file` | Read file contents | "Read my notes.txt file" |
| `write_file` | Create/modify files | "Write a Python script" |
| `list_directory` | Browse file system | "Show me what's in this folder" |
| `web_request` | Make HTTP requests | "Check this API endpoint" |
| `calculate` | Math calculations | "Calculate 2^8 + 15*3" |
| `get_system_info` | System information | "What OS am I running?" |
| `search_text` | Text pattern search | "Find all email addresses" |
| And more... | | |

## 🎨 **Themes**

### **Company Theme** (Default)
- Light background with pink and baby blue accents
- Perfect for professional use

### **Dark Company Theme**
- Dark interface with your company colors
- Easy on the eyes for long sessions

### **Augie Classic**
- Inspired by the original Augie
- Familiar feel with modern touches

### **Light Theme**
- Clean, minimal interface
- Maximum readability

## 🚀 **Roadmap**

With our **5 million token budget**, we're building:

### **Phase 2: Advanced Tools** (Next)
- Database integration tools
- Image processing capabilities
- Code analysis and formatting
- Network diagnostic tools

### **Phase 3: Translation System**
- Real-time AI translation
- 100+ language support
- OCR translation for images

### **Phase 4: Knowledge Management**
- Global search across conversations
- Personal knowledge vault
- Semantic search with embeddings

### **Phase 5: Plugin Ecosystem**
- Plugin marketplace
- Custom mini-programs
- Third-party integrations

*And much more...*

## 🤝 **Contributing**

Augie is open source and welcomes contributions!

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## 📄 **License**

This project is licensed under the AGPL v3 License - see the [LICENSE](LICENSE) file for details.

Built with permission from Cherry Studio's creators, as long as it remains open source and free.

## 🙏 **Acknowledgments**

- **Cherry Studio Team** - For the original inspiration and permission
- **MCP Protocol** - For the Model Context Protocol specification
- **Open Source Community** - For the amazing tools and libraries

---

**Built with ❤️ using Python, Tkinter, and modern AI technologies.**

🍒 **Augie** - Your AI assistant, beautifully branded and infinitely extensible.
