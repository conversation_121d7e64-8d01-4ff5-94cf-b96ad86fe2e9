# MCP Discovery & Local Tools Documentation

This document provides an overview of the MCP (Model Context Protocol) discovery utilities and the extended local tools available in Augie.

## Overview

Augie leverages the Model Context Protocol (MCP) to integrate various tools into the assistant platform. These tools allow <PERSON><PERSON> to perform tasks such as file operations, database queries, image processing, and more, directly from within the assistant interface.

### MCP Discovery Utilities

The `discovery.py` module provides helper functions to probe remote MCP servers (via HTTP) and retrieve available tools and metadata. Key functions include:

- **`probe_http_mcp(base_url, timeout)`**:
  - Probes an HTTP MCP server for endpoints such as `/health`, `/tools`, or metadata endpoints.
  - Returns a dictionary with keys:
    - `healthy`: <PERSON><PERSON><PERSON> indicating if the server is healthy.
    - `tools`: List of discovered tools.
    - `metadata`: Additional metadata from the server.

- **`probe_mcp_server_auto(base_url, api_key, timeout)`**:
  - A higher-level probe function that optionally adds authorization headers.
  - Centralizes authentication for future extensibility.

- **`summarize_tools(tools)`**:
  - Normalizes a list of tool descriptions into a lightweight summary for storage.
  - Extracts tool name, description, and parameter count.

These utilities are used in the MCP Server Manager to auto-probe HTTP MCP servers and update server configurations with discovered tools and metadata.

### Extended Local Tools

The `local_tools.py` module implements a suite of built-in tools that run locally. These include:

- **File Operations**: `read_file`, `write_file`, `list_directory`, `create_directory`
- **Web Requests**: `web_request`, `download_file`
- **System Utilities**: `execute_command`, `get_system_info`, `get_current_time`
- **Text Processing**: `search_text`, `encode_decode`
- **Mathematical Calculations**: `calculate`
- **Database Utilities**: `sqlite_query` (execute read-only SELECT queries against SQLite databases)
- **Image Utilities**: `image_info` (retrieve basic metadata using Pillow)
- **Code Analysis**: `lint_python` (run a simple lint check using flake8)

Each tool is defined using a standard structure which includes:
- **Name**: A unique tool name.
- **Description**: A brief description of what the tool does.
- **Parameters**: A set of parameters required by the tool, including type, description, whether they are required, and default values.
- **Server Name**: Identifier for the local tools server.

### Usage Example

To call a local tool from within the assistant, a typical request might look like:
```
TOOL_CALL: read_file(path='notes.txt')
```
The response will contain the content of the file `notes.txt` if it exists.

### Integration

The MCP Server Manager dynamically dispatches tool calls to the appropriate local tool or remote MCP server. When a tool call is made:
- The local tools server is checked first.
- If the tool is not found locally, connected remote MCP servers are queried.
- Discovery utilities can be used to auto-update server configurations with new tools.

## Conclusion

This documentation outlines the MCP discovery process and the extended local tools available in Augie. These components form the backbone of Augie's flexible and extensible tool integration system, enabling rich interactions and powerful functionality within the assistant.

For further details, refer to the source code in `src/mcp/discovery.py` and `src/mcp/local_tools.py`.

Happy coding!
